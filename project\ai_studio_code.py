# 导入所需的库
import requests
import json

# --- 1. 配置您的参数 (请在这里修改) ---

# ▼▼▼ 粘贴你的长期访问令牌 ▼▼▼
# 注意：您需要先完成Facebook Ad Library API的授权流程
# 1. 访问 https://developers.facebook.com/ 创建开发者账户
# 2. 完成政治广告验证：https://www.facebook.com/ID
# 3. 创建应用程序（类别：其他 -> 商业）
# 4. 添加Facebook登录功能
# 5. 获取具有ads_read权限的访问令牌
ACCESS_TOKEN = 'EAAWZCgkp4ZAZA0BPR1yTAQReyNvUveOlYsZAeruDdFROeBF1Pxc0N8pXBKHYoPKhe7kGQoZA0ntsV1pkichoZBybDdB273sPpc7pdJPzvBByuXkI5N2zCyavSZBZBtU3Afd5JoO9qrJbZCDOCew4LmkZCQOVozhEblgZARJcMH6JNpqWvoTOQufrlSSmt2nIswMqZBZCiDt5wfkId'  # 请替换为您的有效访问令牌
# ▲▲▲ 粘贴你的长期访问令牌 ▲▲▲

SEARCH_TERMS = 'Snapmaker U1'
COUNTRY = 'US'
API_VERSION = 'v21.0'  # 使用最新的API版本

# --- 2. 定义您想获取的广告数据字段 ---
# 基于最新API文档的可用字段
FIELDS_TO_REQUEST = (
    'id,'
    'ad_creation_time,'
    'ad_creative_bodies,'  # 修正：使用复数形式
    'ad_creative_link_titles,'  # 修正：使用复数形式
    'ad_creative_link_descriptions,'  # 新增字段
    'ad_delivery_start_time,'
    'ad_delivery_stop_time,'  # 新增字段
    'ad_snapshot_url,'  # 修正：正确的字段名
    'page_id,'
    'page_name,'
    'publisher_platforms,'  # 修正：使用复数形式
    'languages'  # 新增字段
)

# --- 3. 构建API请求 ---
url = f'https://graph.facebook.com/{API_VERSION}/ads_archive'

# 将所有参数打包
params = {
    'access_token': ACCESS_TOKEN,
    'search_terms': SEARCH_TERMS,
    'ad_type': 'ALL',
    'ad_active_status': 'ALL',  # 新增：指定广告状态

    # ===================================================================
    # 根据最新API文档，使用正确的国家参数格式
    # 支持多个国家：['US', 'CA', 'GB'] 或单个国家：['US']
    'ad_reached_countries': [COUNTRY],  # 修正：使用列表格式而非字符串
    # ===================================================================

    'fields': FIELDS_TO_REQUEST,
    'limit': 25,
    'search_type': 'KEYWORD_UNORDERED'  # 新增：指定搜索类型
}

# --- 4. 验证访问令牌 ---
if ACCESS_TOKEN == 'YOUR_ACCESS_TOKEN_HERE' or not ACCESS_TOKEN:
    print("❌ 错误：请先设置有效的访问令牌！")
    print("\n请按照以下步骤获取访问令牌：")
    print("1. 访问 https://developers.facebook.com/tools/explorer/")
    print("2. 选择您的应用")
    print("3. 添加权限：ads_read")
    print("4. 生成访问令牌")
    print("5. 将令牌复制到代码中的ACCESS_TOKEN变量")
    print("\n详细设置指南请查看：docs/facebook-ad-library-api-setup-guide.md")
    exit(1)

# --- 5. 发起请求并处理响应 ---
print(f"正在使用关键词 '{SEARCH_TERMS}' 在 '{COUNTRY}' 地区搜索广告...")
print(f"API版本: {API_VERSION}")
print(f"请求URL: {url}")

try:
    response = requests.get(url, params=params)
    response.raise_for_status()
    data = response.json()

    # --- 6. 打印搜索结果 ---
    ads_found = data.get('data', [])

    if not ads_found:
        print("\n❌ 没有找到相关的广告。")
        print("可能的原因：")
        print("1. 搜索关键词没有匹配的广告")
        print("2. 指定的国家/地区没有相关广告")
        print("3. 广告可能已经停止投放")
        print("4. API权限限制")
    else:
        print(f"\n✅ 成功！找到了 {len(ads_found)} 条广告。以下是详细结果：")
    
        for index, ad in enumerate(ads_found[:5], 1):
            print("-" * 60)
            print(f"【广告 {index}】")
            print(f"  广告ID: {ad.get('id', 'N/A')}")
            print(f"  广告主页: {ad.get('page_name', 'N/A')}")
            print(f"  投放平台: {ad.get('publisher_platforms', 'N/A')}")  # 修正字段名
            print(f"  广告文案: {ad.get('ad_creative_bodies', ['N/A'])[0] if ad.get('ad_creative_bodies') else 'N/A'}")  # 修正字段名
            print(f"  广告标题: {ad.get('ad_creative_link_titles', ['N/A'])[0] if ad.get('ad_creative_link_titles') else 'N/A'}")  # 新增
            print(f"  广告快照链接: {ad.get('ad_snapshot_url', 'N/A')}")  # 修正字段名
            print(f"  投放开始时间: {ad.get('ad_delivery_start_time', 'N/A')}")
            print(f"  语言: {ad.get('languages', 'N/A')}")
    
    # --- 6. 处理分页信息 ---
    if 'paging' in data and 'next' in data['paging']:
        print("\n" + "="*60)
        print("提示：还有更多结果在下一页。")
        print("下一页数据的API链接是: ", data['paging']['next'])
        print("="*60)

except requests.exceptions.HTTPError as err:
    print(f"\nAPI 请求失败: {err}")
    print("错误详情 (非常重要，请仔细阅读):")
    print(err.response.text)
    print("\n=== Facebook Ad Library API 授权指南 ===")
    print("如果您看到权限错误，请按以下步骤完成授权：")
    print("\n1. 创建Meta开发者账户:")
    print("   访问: https://developers.facebook.com/")
    print("\n2. 完成政治广告验证 (必需):")
    print("   访问: https://www.facebook.com/ID")
    print("   注意：即使不投放政治广告，也需要完成此验证才能访问Ad Library API")
    print("\n3. 创建Facebook应用:")
    print("   - 类别选择: 其他 -> 商业")
    print("   - 开发模式即可，无需上线")
    print("\n4. 添加Facebook登录产品:")
    print("   - 有效的OAuth重定向URI: https://developers.facebook.com/tools/explorer/")
    print("\n5. 获取访问令牌:")
    print("   - 访问Graph API Explorer: https://developers.facebook.com/tools/explorer/")
    print("   - 选择您的应用")
    print("   - 添加权限: ads_read")
    print("   - 生成访问令牌")
    print("\n6. 将访问令牌替换到代码中的ACCESS_TOKEN变量")
    print("\n常见错误原因：")
    print("1. 未完成政治广告身份验证（最常见）")
    print("2. 访问令牌过期或权限不足")
    print("3. 应用未正确配置Facebook登录")
    print("4. API参数格式错误")

except Exception as e:
    print(f"发生了未知错误: {e}")