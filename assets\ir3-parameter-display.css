/* IR3 Parameter Display Section - Technical Specifications Showcase */
/* File: assets/ir3-parameter-display.css */

/* Base Section Styles */
.param-display-section {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  background: #000;
  color: #fff;
}

.param-display-container {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Enhanced Background Layers */
.param-display-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.param-display-gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(16, 185, 129, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #0f172a 50%, #020617 100%);
}

.param-display-animated-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    -45deg,
    transparent 0%,
    rgba(59, 130, 246, 0.08) 25%,
    transparent 50%,
    rgba(16, 185, 129, 0.08) 75%,
    transparent 100%
  );
  background-size: 400% 400%;
  animation: param-display-gradient-shift 20s ease infinite;
}

@keyframes param-display-gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.param-display-grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: param-display-grid-move 30s linear infinite;
}

@keyframes param-display-grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

.param-display-tech-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.06) 50%, transparent 100%),
    linear-gradient(0deg, transparent 0%, rgba(16, 185, 129, 0.06) 50%, transparent 100%);
  background-size: 300px 100%, 100% 300px;
  animation: param-display-tech-lines-move 40s linear infinite;
}

@keyframes param-display-tech-lines-move {
  0% { background-position: -300px 0, 0 -300px; }
  100% { background-position: calc(100% + 300px) 0, 0 calc(100% + 300px); }
}

.param-display-floating-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.param-display-shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.08), rgba(16, 185, 129, 0.08));
  filter: blur(1px);
  animation: param-display-float 25s ease-in-out infinite;
}

.param-display-shape-1 {
  width: 120px;
  height: 120px;
  top: 15%;
  left: 8%;
  animation-delay: 0s;
}

.param-display-shape-2 {
  width: 180px;
  height: 180px;
  top: 55%;
  right: 12%;
  animation-delay: -8s;
}

.param-display-shape-3 {
  width: 90px;
  height: 90px;
  bottom: 25%;
  left: 25%;
  animation-delay: -16s;
}

@keyframes param-display-float {
  0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
  33% { transform: translateY(-30px) rotate(120deg) scale(1.1); }
  66% { transform: translateY(15px) rotate(240deg) scale(0.9); }
}

/* Content Layer */
.param-display-content-layer {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0 24px;
}

.param-display-wrapper {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 0 0 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.param-display-header {
  text-align: center;
  margin-bottom: 40px;
  flex-shrink: 0;
  animation: param-display-fade-in 1s ease-out;
}

.param-display-title-group {
  max-width: 1000px;
  margin: 0 auto;
}

.param-display-pre-title {
  display: inline-block;
  font-size: 13px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 3px;
  color: #3b82f6;
  margin-bottom: 24px;
  padding: 10px 24px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 30px;
  background: rgba(59, 130, 246, 0.08);
  backdrop-filter: blur(12px);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
}

.param-display-main-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 42px;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 24px;
  background: linear-gradient(135deg, #ffffff 0%, #3b82f6 40%, #10b981 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 40px rgba(59, 130, 246, 0.3);
}

.param-display-description {
  font-size: 16px;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.75);
  margin: 0;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Navigation Styles */
.param-display-nav {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 40px;
  flex-wrap: wrap;
  flex-shrink: 0;
}

.param-display-nav-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 20px;
  background: rgba(15, 23, 42, 0.6);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(12px);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.param-display-nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.15), transparent);
  transition: left 0.6s ease;
}

.param-display-nav-btn:hover::before {
  left: 100%;
}

.param-display-nav-btn:hover {
  border-color: rgba(59, 130, 246, 0.6);
  color: #fff;
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(59, 130, 246, 0.25);
  background: rgba(59, 130, 246, 0.1);
}

.param-display-nav-btn.active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(16, 185, 129, 0.15));
  border-color: #3b82f6;
  color: #fff;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
}

.param-display-nav-icon {
  font-size: 16px;
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.5));
}

.param-display-nav-text {
  white-space: nowrap;
  font-weight: 600;
}

.param-display-nav-count {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.4), rgba(16, 185, 129, 0.3));
  color: #fff;
  padding: 4px 10px;
  border-radius: 14px;
  font-size: 11px;
  font-weight: 700;
  min-width: 26px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* Content Styles */
.param-display-content {
  position: relative;
  flex: 1;
  overflow-y: auto;
  padding: 30px 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  margin-bottom: 40px;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

/* Custom Scrollbar Styles */
.param-display-content::-webkit-scrollbar {
  width: 8px;
}

.param-display-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.param-display-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.6), rgba(16, 185, 129, 0.6));
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.param-display-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(16, 185, 129, 0.8));
}

/* Firefox scrollbar */
.param-display-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.6) rgba(255, 255, 255, 0.05);
}

.param-display-category {
  display: none;
  animation: param-display-fade-in 0.6s ease-out;
}

.param-display-category.active {
  display: block;
}

/* Parameter Table Styles */
.param-table {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(15, 23, 42, 0.8);
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Table Header */
.param-table-header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1));
  border-bottom: 2px solid rgba(59, 130, 246, 0.3);
  position: sticky;
  top: 0;
  z-index: 10;
}

.param-table-header .param-table-cell {
  padding: 20px 24px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 1.1rem;
  color: #e2e8f0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: rgba(59, 130, 246, 0.05);
}

.param-table-header .param-table-cell:first-child {
  border-right: 1px solid rgba(59, 130, 246, 0.2);
}

/* Table Rows */
.param-table-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
  opacity: 1;
  transform: translateY(0);
}

.param-table-row:hover {
  background: rgba(59, 130, 246, 0.05);
  transform: translateX(4px);
}

.param-table-row.even {
  background: rgba(15, 23, 42, 0.3);
}

.param-table-row.odd {
  background: rgba(30, 41, 59, 0.3);
}

.param-table-row:hover {
  background: rgba(59, 130, 246, 0.08) !important;
}

.param-display-toggle {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.param-display-toggle:hover {
  color: #fff;
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.param-display-chevron {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
}

.param-display-card.expanded .param-display-chevron {
  transform: rotate(180deg);
}

.param-display-card-content {
  padding: 0 28px 28px;
  max-height: 0;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
}

.param-display-card.expanded .param-display-card-content {
  max-height: 250px;
  padding: 24px 28px 28px;
  opacity: 1;
}

.param-display-value {
  display: inline-block;
  font-size: 20px;
  font-weight: 800;
  color: #3b82f6;
  margin-bottom: 12px;
  text-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
}

.param-display-value.param-display-supported {
  color: #10b981;
  text-shadow: 0 0 15px rgba(16, 185, 129, 0.4);
}

/* Table Cells */
.param-table-cell {
  padding: 16px 24px;
  vertical-align: middle;
  transition: all 0.3s ease;
}

.param-table-cell-name {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  color: #cbd5e1;
  border-right: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
}

.param-table-cell-name::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 0;
  background: linear-gradient(180deg, #3b82f6, #10b981);
  transition: height 0.3s ease;
}

.param-table-row:hover .param-table-cell-name::before {
  height: 60%;
}

.param-table-cell-value {
  color: #3b82f6;
  font-weight: 500;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* Special styling for sensor values */
.param-display-category[data-category="sensors"] .param-table-cell-value {
  font-size: 1.2rem;
  text-align: center;
}

/* Animation Keyframes */
@keyframes param-display-fade-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */

/* 中等屏幕优化 (1200px - 1600px) */
@media (max-width: 1600px) and (min-width: 1200px) {
  .param-display-section {
    min-height: 100vh !important;
    max-height: 100vh !important;
    height: 100vh !important;
    overflow: hidden !important;
  }

  .param-display-container {
    height: 100vh !important;
    max-height: 100vh !important;
    overflow: hidden !important;
  }

  .param-display-wrapper {
    max-width: 1000px !important;
    margin: 0 auto !important;
    padding: 30px 24px 20px 24px !important;
    height: 100% !important;
    max-height: 100vh !important;
    overflow: hidden !important;
  }

  .param-display-content {
    margin-left: 80px !important;
    margin-right: 40px !important;
    padding: 20px 15px !important;
    max-width: 900px !important;
    margin-bottom: 15px !important;
    flex: 1 !important;
    overflow-y: auto !important;
    max-height: calc(100vh - 200px) !important;
  }

  .param-table {
    max-width: 850px !important;
    margin: 0 auto !important;
    width: 100% !important;
  }

  .param-table-header,
  .param-table-row {
    grid-template-columns: 1fr 1.2fr !important;
  }

  .param-table-cell {
    padding: 12px 18px !important;
    font-size: 0.9rem !important;
  }

  .param-table-header .param-table-cell {
    padding: 16px 18px !important;
    font-family: 'Montserrat', sans-serif;
    font-size: 0.95rem !important;
  }

  .param-display-header {
    margin-bottom: 25px !important;
    flex-shrink: 0 !important;
  }

  .param-display-nav {
    margin-bottom: 20px !important;
    flex-shrink: 0 !important;
  }
}

@media (max-width: 1024px) {
  .param-display-wrapper {
    max-width: 900px;
    padding: 50px 0;
  }

  .param-table {
    max-width: 900px;
  }
}

@media (max-width: 768px) {
  .param-display-content-layer {
    padding: 0 20px;
  }

  .param-display-wrapper {
    padding: 40px 0;
  }

  .param-display-header {
    margin-bottom: 60px;
  }

  .param-display-main-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 32px;
  }

  .param-display-description {
    font-size: 15px;
  }

  .param-display-nav {
    gap: 12px;
    margin-bottom: 60px;
  }

  .param-display-nav-btn {
    padding: 12px 16px;
    font-size: 12px;
  }

  .param-display-nav-text {
    display: none;
  }

  /* Responsive table design */
  .param-table-header,
  .param-table-row {
    grid-template-columns: 1fr;
  }

  .param-table-cell-name {
    border-right: none;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    background: rgba(59, 130, 246, 0.05);
  }

  .param-table-cell-value {
    padding-top: 8px;
  }
}

@media (max-width: 480px) {
  .param-display-content-layer {
    padding: 0 16px;
  }

  .param-display-wrapper {
    padding: 30px 0;
  }

  .param-display-header {
    margin-bottom: 50px;
  }

  .param-display-main-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 26px;
  }

  .param-display-description {
    font-size: 14px;
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
  }

  .param-display-nav {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 50px;
    max-width: 100%;
  }

  .param-display-nav-btn {
    padding: 12px 8px;
    font-size: 11px;
    flex-direction: column;
    gap: 4px;
    min-width: auto;
    width: 100%;
    text-align: center;
  }

  .param-display-nav-icon {
    font-size: 18px;
  }

  .param-display-nav-text {
    display: block;
    font-size: 10px;
    line-height: 1.3;
    text-align: center;
    font-weight: 500;
  }

  .param-display-nav-count {
    font-size: 9px;
    min-width: 20px;
    height: 20px;
    line-height: 20px;
    padding: 0;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 移动端参数表格优化 - 单列布局 */
  .param-table {
    border-radius: 8px;
    overflow: hidden;
  }

  .param-table-header {
    display: none; /* 在移动端隐藏表头 */
  }

  .param-table-row {
    display: flex;
    flex-direction: column;
    padding: 16px;
    margin-bottom: 8px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .param-table-row.even {
    background: rgba(255, 255, 255, 0.08);
  }

  .param-table-cell {
    padding: 0;
    border: none;
    text-align: left;
  }

  .param-table-cell-name {
    font-family: 'Montserrat', sans-serif;
    font-size: 12px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 4px;
  }

  .param-table-cell-value {
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
  }
}
