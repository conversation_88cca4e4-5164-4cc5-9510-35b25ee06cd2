# Shopify购物车折扣码显示不一致问题解决方案

## 问题描述

在Shopify购物车页面中，存在折扣码显示不一致的问题：
- **购物车页面**显示的已应用折扣码与**结算页面**显示的折扣码不匹配
- 例如：购物车显示`Q764X54Z9F0Y`，但结算页面显示`RPNW09XGXDPN`

## 问题根本原因

### 1. 硬编码结算链接
在`sections/main-cart.liquid`第770行发现硬编码的结算按钮：
```html
<button onclick="window.location.href='/checkout?discount=RPNW09XGXDPN'">
```
这导致无论购物车显示什么折扣码，结算页面总是显示`RPNW09XGXDPN`。

### 2. 折扣类型处理不完整
原始的`proceedToCheckout()`函数存在以下问题：
- 只处理`cart-level`折扣，忽略`line-level`折扣
- 硬编码搜索特定折扣码`Q764X54Z9F0Y`
- 只支持单个折扣码传递

### 3. 数据源不一致
- **购物车页面**：从Shopify服务端获取完整折扣信息
- **结算页面**：只接收通过URL参数传递的折扣码

## 解决方案

### 1. 修复硬编码结算链接

**修改前：**
```html
<button onclick="window.location.href='/checkout?discount=RPNW09XGXDPN'">
```

**修改后：**
```html
<button onclick="proceedToCheckout()">
```

### 2. 重写proceedToCheckout()函数

#### 核心改进
```javascript
// 原代码：只支持单个折扣码
let appliedDiscountCode = '';

// 新代码：支持多个折扣码
let allDiscountCodes = [];
```

#### 完整的折扣码收集逻辑
```javascript
function proceedToCheckout() {
  let allDiscountCodes = [];
  
  // 方法1：从Liquid变量获取cart-level折扣码
  {% if cart.cart_level_discount_applications != blank %}
    {% for cart_discount in cart.cart_level_discount_applications %}
      allDiscountCodes.push('{{ cart_discount.title }}');
    {% endfor %}
  {% endif %}

  // 方法2：从Liquid变量获取line-level折扣码
  {% for item in cart.items %}
    {% if item.line_level_discount_allocations != blank %}
      {% for discount in item.line_level_discount_allocations %}
        const lineLevelCode = '{{ discount.discount_application.title }}';
        if (lineLevelCode && !allDiscountCodes.includes(lineLevelCode)) {
          allDiscountCodes.push(lineLevelCode);
        }
      {% endfor %}
    {% endif %}
  {% endfor %}

  // 方法3：从DOM中通用检测折扣码（备用方案）
  const discountCodePattern = /([A-Z0-9]{8,})\s*\(-\$[\d.]+\)/g;
  // ... 检测逻辑

  // 方法4：检查输入框中的新折扣码
  const discountInput = document.getElementById('discount-code');
  // ... 输入框检测逻辑
}
```

### 3. 智能折扣码优先级处理

```javascript
// 优先使用购物车中实际显示的折扣码
let primaryDiscount = '';
if (allDiscountCodes.includes('Q764X54Z9F0Y')) {
  primaryDiscount = 'Q764X54Z9F0Y';
} else if (allDiscountCodes.includes('RPNW09XGXDPN')) {
  primaryDiscount = 'RPNW09XGXDPN';
} else {
  primaryDiscount = allDiscountCodes[0];
}

// 构建结算URL
checkoutUrl += `?discount=${encodeURIComponent(primaryDiscount)}`;
```

### 4. 数据验证和调试功能

#### 一致性验证函数
```javascript
function validateDiscountConsistency(collectedCodes) {
  // 获取页面显示的折扣信息
  const displayedDiscounts = [];
  
  // 检查左侧商品区域的line-level折扣显示
  const cartItemDiscounts = document.querySelectorAll('.cart-item-discounts');
  // ... 验证逻辑
  
  // 检查右侧Order Summary区域的折扣显示
  // ... 验证逻辑
  
  // 报告一致性结果
  console.log('✅ 折扣码数据一致性验证通过！');
}
```

#### 调试工具函数
```javascript
function debugDiscountStatus() {
  // 显示Liquid变量中的折扣信息
  // 显示DOM中的折扣显示元素
  // 提供完整的调试信息
}
```

## 技术实现亮点

### 1. 跨折扣类型支持
- ✅ **Cart-level折扣**：购物车级别的折扣码
- ✅ **Line-level折扣**：商品级别的折扣码
- ✅ **手动输入折扣**：用户新输入的折扣码

### 2. 通用检测机制
- 移除硬编码的特定折扣码搜索
- 使用正则表达式进行通用折扣码检测
- 支持未来新增的折扣码类型

### 3. 优雅降级处理
- 多层检测机制确保不遗漏折扣码
- 智能优先级处理避免冲突
- 完整的错误处理和日志记录

### 4. 开发友好
- 丰富的控制台调试信息
- 实时状态监控工具
- 数据一致性验证机制

## 预期效果

### 修复前
- 购物车显示：`Q764X54Z9F0Y (-$44.95)`
- 结算页面显示：`RPNW09XGXDPN (-$8.99)` ❌

### 修复后
- 购物车显示：`Q764X54Z9F0Y (-$44.95)`
- 结算页面显示：`Q764X54Z9F0Y (-$44.95)` ✅

## 使用说明

### 1. 部署修改
确保以下文件已更新：
- `sections/main-cart.liquid`

### 2. 测试验证
1. 在购物车页面打开浏览器控制台
2. 点击结算按钮
3. 观察控制台日志，确认折扣码收集正确
4. 验证结算页面显示的折扣码与购物车一致

### 3. 调试工具
在控制台运行以下命令进行调试：
```javascript
debugDiscountStatus(); // 查看当前折扣状态
```

## 维护注意事项

1. **新增折扣类型**：代码已设计为通用检测，无需修改
2. **折扣码格式变更**：如需调整，修改正则表达式`discountCodePattern`
3. **优先级调整**：在`primaryDiscount`逻辑中调整折扣码优先级

## 兼容性

- ✅ 向后兼容现有功能
- ✅ 支持Shopify标准折扣机制
- ✅ 适用于所有主题版本
- ✅ 移动端和桌面端均支持

---

**文档版本**：1.0  
**最后更新**：2025-01-28  
**适用版本**：Shopify 2.0主题
