# Augment Code MCP 配置指南

## 概述

本指南将帮助您在Augment Code中配置Facebook Ads Library MCP服务器，实现直接在AI对话中调用Facebook广告库数据。

## 🚀 快速配置

### 步骤1: 确认MCP服务器已安装

MCP服务器已成功安装在：
```
E:\CLT\E工作\shopify\DM\gitee_shopify_motion_augment_gitee\project\facebook-ads-library-mcp\facebook-ads-library-mcp\
```

### 步骤2: 配置API密钥

编辑 `.env` 文件，设置您的API密钥：

```bash
# 必需：ScrapeCreators API密钥
SCRAPECREATORS_API_KEY=your_actual_api_key_here

# 可选：Google Gemini API密钥（用于视频分析）
GEMINI_API_KEY=your_gemini_api_key_here
```

**获取API密钥：**
- ScrapeCreators API: https://scrapecreators.com/
- Google Gemini API: https://aistudio.google.com/app/apikey

### 步骤3: 配置Augment Code

在Augment Code中添加MCP服务器配置。根据您使用的客户端选择相应的配置方法：

#### 方法A: Claude Desktop配置

文件位置：`%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "fb_ad_library": {
      "command": "python",
      "args": [
        "E:\\CLT\\E工作\\shopify\\DM\\gitee_shopify_motion_augment_gitee\\project\\facebook-ads-library-mcp\\facebook-ads-library-mcp\\mcp_server.py"
      ]
    }
  }
}
```

#### 方法B: Cursor配置

文件位置：`%USERPROFILE%\.cursor\mcp.json`

```json
{
  "mcpServers": {
    "fb_ad_library": {
      "command": "python",
      "args": [
        "E:\\CLT\\E工作\\shopify\\DM\\gitee_shopify_motion_augment_gitee\\project\\facebook-ads-library-mcp\\facebook-ads-library-mcp\\mcp_server.py"
      ]
    }
  }
}
```

#### 方法C: 通用MCP客户端配置

如果您使用其他MCP客户端，请使用以下配置：

```json
{
  "name": "fb_ad_library",
  "command": "python",
  "args": [
    "E:\\CLT\\E工作\\shopify\\DM\\gitee_shopify_motion_augment_gitee\\project\\facebook-ads-library-mcp\\facebook-ads-library-mcp\\mcp_server.py"
  ],
  "env": {
    "SCRAPECREATORS_API_KEY": "your_api_key_here"
  }
}
```

### 步骤4: 重启客户端

配置完成后，重启您的AI客户端（Claude Desktop、Cursor等）以加载MCP服务器。

## 🔧 可用功能

配置成功后，您可以在对话中使用以下功能：

### 1. 搜索Facebook广告

```
请搜索"Snapmaker U1"的Facebook广告数据
```

### 2. 分析广告策略

```
分析Apple iPhone 15的广告投放策略
```

### 3. 获取竞品广告信息

```
获取Tesla在美国市场的广告投放情况
```

### 4. 生成广告分析报告

```
生成Nike运动鞋广告的详细分析报告
```

## 📊 MCP工具列表

| 工具名称 | 功能描述 | 参数 |
|----------|----------|------|
| `search_facebook_ads` | 搜索Facebook广告 | search_terms, countries, limit |
| `get_ad_details` | 获取广告详细信息 | ad_id |
| `analyze_ad_performance` | 分析广告表现 | search_terms, date_range |
| `generate_report` | 生成分析报告 | data, format |

## 🔍 测试配置

### 测试MCP连接

在AI对话中输入：

```
请测试Facebook Ads Library MCP连接是否正常
```

### 测试搜索功能

```
搜索"test"关键词的Facebook广告，限制返回5条结果
```

### 验证数据格式

```
显示Facebook广告数据的字段结构
```

## ⚠️ 故障排除

### 常见问题

1. **MCP服务器无法启动**
   - 检查Python环境是否正确
   - 确认所有依赖包已安装
   - 验证文件路径是否正确

2. **API调用失败**
   - 检查API密钥是否正确设置
   - 确认网络连接正常
   - 验证API配额是否充足

3. **配置文件错误**
   - 检查JSON格式是否正确
   - 确认文件路径使用双反斜杠
   - 验证配置文件位置是否正确

### 调试步骤

1. **检查MCP服务器状态**
   ```bash
   cd project/facebook-ads-library-mcp/facebook-ads-library-mcp
   python mcp_server.py --test
   ```

2. **验证API密钥**
   ```bash
   python -c "import os; from dotenv import load_dotenv; load_dotenv(); print('API Key:', os.getenv('SCRAPECREATORS_API_KEY')[:10] + '...')"
   ```

3. **测试网络连接**
   ```bash
   curl -X GET "https://api.scrapecreators.com/health"
   ```

## 📝 配置文件模板

### Claude Desktop完整配置

```json
{
  "globalShortcut": "Ctrl+Shift+C",
  "mcpServers": {
    "fb_ad_library": {
      "command": "python",
      "args": [
        "E:\\CLT\\E工作\\shopify\\DM\\gitee_shopify_motion_augment_gitee\\project\\facebook-ads-library-mcp\\facebook-ads-library-mcp\\mcp_server.py"
      ],
      "env": {
        "PYTHONPATH": "E:\\CLT\\E工作\\shopify\\DM\\gitee_shopify_motion_augment_gitee\\project\\facebook-ads-library-mcp\\facebook-ads-library-mcp"
      }
    }
  }
}
```

### 环境变量配置

如果需要在系统级别设置环境变量：

```bash
# Windows PowerShell
$env:SCRAPECREATORS_API_KEY="your_api_key_here"
$env:GEMINI_API_KEY="your_gemini_api_key_here"

# Windows CMD
set SCRAPECREATORS_API_KEY=your_api_key_here
set GEMINI_API_KEY=your_gemini_api_key_here
```

## 🔄 更新和维护

### 更新MCP服务器

```bash
cd project/facebook-ads-library-mcp/facebook-ads-library-mcp
git pull origin main
pip install -r requirements.txt --upgrade
```

### 备份配置

定期备份您的配置文件：
- `.env` 文件（包含API密钥）
- MCP客户端配置文件
- 自定义脚本和工具

### 监控使用情况

- 定期检查API使用配额
- 监控MCP服务器性能
- 更新API密钥（如有需要）

## 📞 技术支持

如果遇到问题，可以：

1. 查看MCP服务器日志
2. 检查API提供商的状态页面
3. 参考官方MCP文档
4. 联系技术支持团队

---

*配置完成后，您就可以在Augment Code中直接使用Facebook Ads Library的强大功能了！*
