# Shopify购物车折扣码系统完整技术实现方案

## 系统架构概览

### 核心组件
1. **折扣码应用系统** - 处理用户输入和验证
2. **折扣码显示系统** - 管理左右两侧的折扣显示
3. **折扣码清除系统** - 提供移除功能
4. **结算页面同步系统** - 确保数据一致性传递

### 数据流向
```
用户输入 → 验证处理 → Shopify API → 页面更新 → 结算同步
```

## 1. 折扣码应用系统

### 1.1 用户界面组件
```html
<!-- 折扣码输入区域 -->
<div class="discount-code-section">
  <label for="discount-code" class="discount-code-label">Discount Code</label>
  <div class="discount-code-input-wrapper">
    <input type="text" id="discount-code" class="discount-code-input" placeholder="Enter discount code">
    <button type="button" class="discount-code-btn" onclick="applyDiscountCode()">Apply</button>
  </div>
  <div id="discount-applied" class="discount-applied" style="display: none;">
    <span id="discount-text"></span>
    <button type="button" class="remove-discount" onclick="removeDiscountCode()">×</button>
  </div>
</div>
```

### 1.2 折扣码应用核心函数
```javascript
function applyDiscountCode() {
  const discountInput = document.getElementById('discount-code');
  const discountCode = discountInput.value.trim().toUpperCase();

  // 1. 输入验证
  if (!discountCode) {
    showErrorMessage('Please enter a discount code');
    return;
  }

  // 2. 格式验证
  if (discountCode.length < 3) {
    showErrorMessage('Discount code must be at least 3 characters long');
    return;
  }

  if (!/^[A-Z0-9]+$/.test(discountCode)) {
    showErrorMessage('Discount code can only contain letters and numbers');
    return;
  }

  // 3. 显示加载状态
  const applyBtn = document.querySelector('.discount-code-btn');
  const originalText = applyBtn.textContent;
  applyBtn.textContent = 'Applying...';
  applyBtn.disabled = true;

  // 4. 跳转到Shopify折扣应用页面
  const currentUrl = window.location.href;
  const redirectUrl = encodeURIComponent('/cart');
  window.location.href = `/discount/${encodeURIComponent(discountCode)}?redirect=${redirectUrl}`;
}
```

### 1.3 输入增强功能
```javascript
// 回车键支持
discountInput.addEventListener('keypress', function(e) {
  if (e.key === 'Enter') {
    applyDiscountCode();
  }
});

// 自动转大写
discountInput.addEventListener('input', function() {
  this.value = this.value.toUpperCase();
});

// 清除错误消息
discountInput.addEventListener('input', function() {
  hideAllMessages();
});
```

## 2. 折扣码显示系统

### 2.1 左侧商品区域显示（Line-Level折扣）
```liquid
<!-- 在cart-item-modern.liquid中 -->
{%- if product.line_level_discount_allocations != blank -%}
  <div class="cart-item-discounts">
    {%- for discount_allocation in product.line_level_discount_allocations -%}
      <div>{{ discount_allocation.discount_application.title }} (-{{ discount_allocation.amount | money }})</div>
    {%- endfor -%}
  </div>
{%- endif -%}
```

### 2.2 右侧Order Summary显示（Cart-Level折扣）
```liquid
<!-- 在main-cart.liquid中 -->
{%- if cart.cart_level_discount_applications != blank -%}
  <div class="cart-discounts">
    {%- for discount_application in cart.cart_level_discount_applications -%}
      <div class="cart-discount-item">
        <span class="discount-title">{{ discount_application.title }}</span>
        <span class="discount-amount">-{{ discount_application.total_allocated_amount | money }}</span>
        <button type="button" class="remove-discount" onclick="removeDiscountCode()">×</button>
      </div>
    {%- endfor -%}
  </div>
{%- endif -%}
```

### 2.3 动态折扣显示增强
```javascript
// 自定义右侧折扣显示（用于Line-Level折扣）
function displayAllDiscounts() {
  const orderSummary = document.querySelector('.cart-summary-details');
  if (!orderSummary) return false;

  // 移除现有显示
  const existingDiscounts = orderSummary.querySelector('.custom-discounts-display');
  if (existingDiscounts) existingDiscounts.remove();

  // 收集所有折扣
  let allDiscounts = [];

  // 从DOM检测Line-Level折扣
  const discountElements = document.querySelectorAll('*');
  const discountCodePattern = /([A-Z0-9]{8,})\s*\(-\$[\d.]+\)/g;
  
  for (let element of discountElements) {
    const text = element.textContent || '';
    let match;
    while ((match = discountCodePattern.exec(text)) !== null) {
      const foundCode = match[1];
      if (!allDiscounts.some(d => d.code === foundCode)) {
        allDiscounts.push({
          code: foundCode,
          amount: match[0].match(/-\$[\d.]+/)[0],
          type: 'line-level'
        });
      }
    }
  }

  // 创建右侧显示
  if (allDiscounts.length > 0) {
    const discountsHTML = `
      <div class="custom-discounts-display">
        <div class="discount-header">Applied Discounts</div>
        ${allDiscounts.map(discount => `
          <div class="discount-item">
            <span>${discount.code} (${discount.amount})</span>
            <button onclick="removeDiscountCode()">×</button>
          </div>
        `).join('')}
      </div>
    `;
    orderSummary.insertAdjacentHTML('afterbegin', discountsHTML);
    return true;
  }
  return false;
}
```

## 3. 折扣码清除系统

### 3.1 清除函数实现
```javascript
async function removeDiscountCode() {
  console.log('🧹 Removing all discount codes...');
  
  try {
    // 方法1：使用Cart API清除
    const response = await fetch('/cart/update.js', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ discount_codes: [] })
    });
    
    if (response.ok) {
      console.log('✅ Discount codes cleared successfully');
      location.reload();
    } else {
      console.error('❌ Failed to clear discount codes');
      // 方法2：备用清除方式
      fallbackClearDiscount();
    }
  } catch (error) {
    console.error('❌ Error clearing discount codes:', error);
    // 方法2：备用清除方式
    fallbackClearDiscount();
  }
}

// 备用清除方式
function fallbackClearDiscount() {
  // 使用CLEAR折扣码清除
  window.location.href = '/discount/CLEAR?redirect=' + encodeURIComponent('/cart');
}
```

### 3.2 清除按钮样式和交互
```css
.remove-discount {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.remove-discount:hover {
  background-color: #dc3545;
  color: white;
  transform: scale(1.1);
}
```

## 4. 结算页面同步系统

### 4.1 智能折扣码收集
```javascript
function proceedToCheckout() {
  let allDiscountCodes = [];
  
  // 收集Cart-Level折扣
  {% if cart.cart_level_discount_applications != blank %}
    {% for cart_discount in cart.cart_level_discount_applications %}
      allDiscountCodes.push('{{ cart_discount.title }}');
    {% endfor %}
  {% endif %}

  // 收集Line-Level折扣
  {% for item in cart.items %}
    {% if item.line_level_discount_allocations != blank %}
      {% for discount in item.line_level_discount_allocations %}
        const lineLevelCode = '{{ discount.discount_application.title }}';
        if (lineLevelCode && !allDiscountCodes.includes(lineLevelCode)) {
          allDiscountCodes.push(lineLevelCode);
        }
      {% endfor %}
    {% endif %}
  {% endfor %}

  // DOM备用检测
  const discountElements = document.querySelectorAll('*');
  const discountCodePattern = /([A-Z0-9]{8,})\s*\(-\$[\d.]+\)/g;
  
  for (let element of discountElements) {
    const text = element.textContent || '';
    let match;
    while ((match = discountCodePattern.exec(text)) !== null) {
      const foundCode = match[1];
      if (!allDiscountCodes.includes(foundCode)) {
        allDiscountCodes.push(foundCode);
      }
    }
  }

  // 检查输入框
  const discountInput = document.getElementById('discount-code');
  if (discountInput && discountInput.value.trim()) {
    const inputCode = discountInput.value.trim().toUpperCase();
    if (!allDiscountCodes.includes(inputCode)) {
      allDiscountCodes.push(inputCode);
    }
  }

  return allDiscountCodes;
}
```

### 4.2 优先级处理和URL构建
```javascript
function buildCheckoutUrl(allDiscountCodes) {
  let checkoutUrl = '/checkout';
  
  if (allDiscountCodes.length > 0) {
    // 智能优先级选择
    let primaryDiscount = '';
    if (allDiscountCodes.includes('Q764X54Z9F0Y')) {
      primaryDiscount = 'Q764X54Z9F0Y';
    } else if (allDiscountCodes.includes('RPNW09XGXDPN')) {
      primaryDiscount = 'RPNW09XGXDPN';
    } else {
      primaryDiscount = allDiscountCodes[0];
    }
    
    checkoutUrl += `?discount=${encodeURIComponent(primaryDiscount)}`;
    console.log('🔗 构建结算URL:', checkoutUrl);
  }
  
  return checkoutUrl;
}
```

### 4.3 数据一致性验证
```javascript
function validateDiscountConsistency(collectedCodes) {
  const displayedDiscounts = [];
  
  // 检查左侧显示
  const cartItemDiscounts = document.querySelectorAll('.cart-item-discounts');
  cartItemDiscounts.forEach(element => {
    const text = element.textContent.trim();
    const matches = text.match(/([A-Z0-9]{8,})\s*\(-\$[\d.]+\)/g);
    if (matches) {
      matches.forEach(match => {
        const code = match.match(/([A-Z0-9]{8,})/)[1];
        if (!displayedDiscounts.includes(code)) {
          displayedDiscounts.push(code);
        }
      });
    }
  });
  
  // 检查右侧显示
  const orderSummaryDiscounts = document.querySelectorAll('.cart-summary-details *');
  orderSummaryDiscounts.forEach(element => {
    const text = element.textContent.trim();
    if (text.includes('discount') || text.includes('Discount')) {
      const matches = text.match(/([A-Z0-9]{8,})/g);
      if (matches) {
        matches.forEach(code => {
          if (code.length >= 8 && !displayedDiscounts.includes(code)) {
            displayedDiscounts.push(code);
          }
        });
      }
    }
  });
  
  // 验证一致性
  const missingFromCollection = displayedDiscounts.filter(code => !collectedCodes.includes(code));
  const extraInCollection = collectedCodes.filter(code => !displayedDiscounts.includes(code));
  
  return {
    consistent: missingFromCollection.length === 0 && extraInCollection.length === 0,
    displayed: displayedDiscounts,
    collected: collectedCodes,
    missing: missingFromCollection,
    extra: extraInCollection
  };
}
```

## 5. 系统初始化和事件绑定

### 5.1 页面加载初始化
```javascript
document.addEventListener('DOMContentLoaded', function() {
  // 初始化折扣码输入功能
  const discountInput = document.getElementById('discount-code');
  if (discountInput) {
    // 绑定事件监听器
    discountInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') applyDiscountCode();
    });
    
    discountInput.addEventListener('input', function() {
      this.value = this.value.toUpperCase();
      hideAllMessages();
    });
  }
  
  // 初始化折扣显示
  displayAllDiscounts();
  
  // 设置调试工具
  if (window.location.hostname.includes('myshopify.com')) {
    window.debugDiscountStatus = debugDiscountStatus;
    console.log('💡 调试提示: 运行 debugDiscountStatus() 查看折扣状态');
  }
});
```

### 5.2 调试和监控工具
```javascript
function debugDiscountStatus() {
  console.log('🛠️ 折扣码状态调试工具');
  console.log('='.repeat(50));
  
  // 显示Liquid变量中的折扣信息
  {% if cart.cart_level_discount_applications != blank %}
    console.log('Cart-level折扣:');
    {% for cart_discount in cart.cart_level_discount_applications %}
      console.log('  - {{ cart_discount.title }}: {{ cart_discount.total_allocated_amount | money }}');
    {% endfor %}
  {% endif %}
  
  {% for item in cart.items %}
    {% if item.line_level_discount_allocations != blank %}
      console.log('Line-level折扣 ({{ item.product.title }}):');
      {% for discount in item.line_level_discount_allocations %}
        console.log('  - {{ discount.discount_application.title }}: {{ discount.amount | money }}');
      {% endfor %}
    {% endif %}
  {% endfor %}
  
  // 显示DOM中的折扣显示
  const cartItemDiscounts = document.querySelectorAll('.cart-item-discounts');
  if (cartItemDiscounts.length > 0) {
    console.log('DOM折扣显示:');
    cartItemDiscounts.forEach((element, index) => {
      console.log(`  ${index + 1}. ${element.textContent.trim()}`);
    });
  }
  
  console.log('='.repeat(50));
}
```

## 6. 错误处理和用户反馈

### 6.1 消息显示系统
```javascript
function showErrorMessage(message) {
  hideAllMessages();
  const errorDiv = document.createElement('div');
  errorDiv.className = 'discount-error-message';
  errorDiv.textContent = message;
  
  const inputWrapper = document.querySelector('.discount-code-input-wrapper');
  inputWrapper.appendChild(errorDiv);
  
  setTimeout(() => {
    errorDiv.remove();
  }, 5000);
}

function showSuccessMessage(message) {
  hideAllMessages();
  const successDiv = document.createElement('div');
  successDiv.className = 'discount-success-message';
  successDiv.textContent = message;
  
  const inputWrapper = document.querySelector('.discount-code-input-wrapper');
  inputWrapper.appendChild(successDiv);
  
  setTimeout(() => {
    successDiv.remove();
  }, 3000);
}

function hideAllMessages() {
  const messages = document.querySelectorAll('.discount-error-message, .discount-success-message');
  messages.forEach(msg => msg.remove());
}
```

### 6.2 加载状态管理
```javascript
function setLoadingState(isLoading) {
  const applyBtn = document.querySelector('.discount-code-btn');
  const discountInput = document.getElementById('discount-code');
  
  if (isLoading) {
    applyBtn.textContent = 'Applying...';
    applyBtn.disabled = true;
    discountInput.disabled = true;
  } else {
    applyBtn.textContent = 'Apply';
    applyBtn.disabled = false;
    discountInput.disabled = false;
  }
}
```

## 7. 样式和用户体验

### 7.1 核心样式
```css
.discount-code-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.discount-code-input-wrapper {
  display: flex;
  gap: 10px;
  margin-top: 8px;
}

.discount-code-input {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.discount-code-btn {
  padding: 12px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
}

.discount-code-btn:hover {
  background-color: #0056b3;
}

.discount-code-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}
```

### 7.2 响应式设计
```css
@media (max-width: 768px) {
  .discount-code-input-wrapper {
    flex-direction: column;
  }
  
  .discount-code-btn {
    width: 100%;
  }
}
```

## 8. Shopify API集成详解

### 8.1 折扣码应用流程
```
用户输入折扣码 → 前端验证 → 跳转到Shopify折扣页面 → Shopify处理 → 重定向回购物车
```

#### Shopify折扣URL格式
```javascript
// 基本格式
/discount/{DISCOUNT_CODE}?redirect={REDIRECT_URL}

// 实际示例
/discount/SAVE20?redirect=%2Fcart

// 清除折扣码
/discount/CLEAR?redirect=%2Fcart
```

### 8.2 Cart API集成
```javascript
// 获取购物车数据
async function getCartData() {
  try {
    const response = await fetch('/cart.js');
    const cart = await response.json();
    return cart;
  } catch (error) {
    console.error('获取购物车数据失败:', error);
    return null;
  }
}

// 更新购物车（清除折扣码）
async function updateCart(updates) {
  try {
    const response = await fetch('/cart/update.js', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates)
    });

    if (response.ok) {
      const cart = await response.json();
      return cart;
    } else {
      throw new Error('更新购物车失败');
    }
  } catch (error) {
    console.error('更新购物车错误:', error);
    return null;
  }
}
```

### 8.3 Liquid模板数据结构
```liquid
<!-- Cart-Level折扣数据结构 -->
{% for discount in cart.cart_level_discount_applications %}
  折扣标题: {{ discount.title }}
  折扣类型: {{ discount.type }}
  折扣金额: {{ discount.total_allocated_amount | money }}
  折扣目标: {{ discount.target_type }}
{% endfor %}

<!-- Line-Level折扣数据结构 -->
{% for item in cart.items %}
  {% for discount in item.line_level_discount_allocations %}
    商品: {{ item.product.title }}
    折扣标题: {{ discount.discount_application.title }}
    折扣类型: {{ discount.discount_application.type }}
    折扣金额: {{ discount.amount | money }}
    分配类型: {{ discount.discount_application.allocation_method }}
  {% endfor %}
{% endfor %}
```

## 9. 性能优化策略

### 9.1 DOM操作优化
```javascript
// 批量DOM更新
function updateDiscountDisplay(discounts) {
  // 使用DocumentFragment减少重排
  const fragment = document.createDocumentFragment();

  discounts.forEach(discount => {
    const discountElement = createDiscountElement(discount);
    fragment.appendChild(discountElement);
  });

  // 一次性插入DOM
  const container = document.querySelector('.discount-container');
  container.innerHTML = '';
  container.appendChild(fragment);
}

// 防抖处理用户输入
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 应用防抖到输入验证
const debouncedValidation = debounce(validateDiscountInput, 300);
```

### 9.2 缓存策略
```javascript
// 折扣码验证缓存
const discountValidationCache = new Map();

function getCachedValidation(code) {
  const cached = discountValidationCache.get(code);
  if (cached && Date.now() - cached.timestamp < 300000) { // 5分钟缓存
    return cached.result;
  }
  return null;
}

function setCachedValidation(code, result) {
  discountValidationCache.set(code, {
    result: result,
    timestamp: Date.now()
  });
}
```

## 10. 安全性考虑

### 10.1 输入验证和清理
```javascript
function sanitizeDiscountCode(input) {
  // 移除危险字符
  const sanitized = input
    .replace(/[<>\"'&]/g, '') // 移除HTML特殊字符
    .replace(/[^\w\-]/g, '')   // 只保留字母数字和连字符
    .toUpperCase()
    .trim();

  // 长度限制
  if (sanitized.length > 50) {
    return sanitized.substring(0, 50);
  }

  return sanitized;
}

function validateDiscountCodeFormat(code) {
  // 基本格式验证
  const validPattern = /^[A-Z0-9\-]{3,50}$/;
  return validPattern.test(code);
}
```

### 10.2 XSS防护
```javascript
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

function createSafeDiscountElement(discountData) {
  const element = document.createElement('div');
  element.className = 'discount-item';

  // 安全地设置文本内容
  const codeSpan = document.createElement('span');
  codeSpan.textContent = discountData.code; // 使用textContent而不是innerHTML

  const amountSpan = document.createElement('span');
  amountSpan.textContent = discountData.amount;

  element.appendChild(codeSpan);
  element.appendChild(amountSpan);

  return element;
}
```

## 11. 错误处理和恢复机制

### 11.1 网络错误处理
```javascript
async function applyDiscountWithRetry(code, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await applyDiscount(code);
      return result;
    } catch (error) {
      console.warn(`折扣码应用失败，尝试 ${attempt}/${maxRetries}:`, error);

      if (attempt === maxRetries) {
        // 最后一次尝试失败，显示用户友好的错误消息
        showErrorMessage('网络连接问题，请稍后重试');
        throw error;
      }

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
}
```

### 11.2 状态恢复机制
```javascript
// 保存应用状态
function saveDiscountState() {
  const state = {
    appliedCodes: getAllAppliedDiscountCodes(),
    timestamp: Date.now()
  };

  try {
    localStorage.setItem('discount_state', JSON.stringify(state));
  } catch (error) {
    console.warn('无法保存折扣状态:', error);
  }
}

// 恢复应用状态
function restoreDiscountState() {
  try {
    const saved = localStorage.getItem('discount_state');
    if (saved) {
      const state = JSON.parse(saved);

      // 检查状态是否过期（1小时）
      if (Date.now() - state.timestamp < 3600000) {
        return state.appliedCodes;
      }
    }
  } catch (error) {
    console.warn('无法恢复折扣状态:', error);
  }

  return [];
}
```

## 12. 测试和调试工具

### 12.1 自动化测试辅助
```javascript
// 测试工具函数
window.discountTestUtils = {
  // 模拟折扣码应用
  simulateDiscountApplication: function(code) {
    const input = document.getElementById('discount-code');
    input.value = code;
    applyDiscountCode();
  },

  // 获取当前折扣状态
  getCurrentDiscountState: function() {
    return {
      displayed: getDisplayedDiscounts(),
      applied: getAppliedDiscounts(),
      inputValue: document.getElementById('discount-code').value
    };
  },

  // 清除所有折扣
  clearAllDiscounts: function() {
    removeDiscountCode();
  },

  // 验证折扣一致性
  validateConsistency: function() {
    const collected = proceedToCheckout();
    return validateDiscountConsistency(collected);
  }
};
```

### 12.2 性能监控
```javascript
// 性能监控工具
const performanceMonitor = {
  timers: new Map(),

  start: function(label) {
    this.timers.set(label, performance.now());
  },

  end: function(label) {
    const startTime = this.timers.get(label);
    if (startTime) {
      const duration = performance.now() - startTime;
      console.log(`⏱️ ${label}: ${duration.toFixed(2)}ms`);
      this.timers.delete(label);
      return duration;
    }
  },

  measure: function(label, fn) {
    this.start(label);
    const result = fn();
    this.end(label);
    return result;
  }
};

// 使用示例
performanceMonitor.measure('折扣码应用', () => {
  applyDiscountCode();
});
```

## 13. 国际化和本地化

### 13.1 多语言支持
```javascript
const discountMessages = {
  'en': {
    'apply': 'Apply',
    'applying': 'Applying...',
    'remove': 'Remove',
    'error_empty': 'Please enter a discount code',
    'error_invalid': 'Invalid discount code format',
    'success_applied': 'Discount code applied successfully'
  },
  'zh': {
    'apply': '应用',
    'applying': '应用中...',
    'remove': '移除',
    'error_empty': '请输入折扣码',
    'error_invalid': '折扣码格式无效',
    'success_applied': '折扣码应用成功'
  }
};

function getMessage(key) {
  const locale = document.documentElement.lang || 'en';
  return discountMessages[locale]?.[key] || discountMessages['en'][key] || key;
}
```

### 13.2 货币格式化
```javascript
function formatMoney(amount, currency = 'USD') {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount / 100); // Shopify金额以分为单位
}

// Shopify主题设置集成
function formatMoneyWithThemeSettings(amount) {
  if (window.theme && window.theme.moneyFormat) {
    return window.theme.Currency.formatMoney(amount, window.theme.moneyFormat);
  }
  return formatMoney(amount);
}
```

---

**技术方案版本**：1.0
**适用环境**：Shopify 2.0主题
**最后更新**：2025-01-28
**文档状态**：完整技术实现方案
