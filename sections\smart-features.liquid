{{ 'ir3-smart-features.css' | asset_url | stylesheet_tag }}
<script src="{{ 'ir3-smart-features.js' | asset_url }}" defer></script>

<section
  class="smart-features-section"
  id="smart-features-{{ section.id }}"
  data-section-id="{{ section.id }}"
>
  <!-- Apple-style Background -->
  <div class="apple-background">
    <div class="gradient-overlay"></div>
  </div>

  <!-- Content Container -->
  <div class="smart-container">
    <!-- Title Section -->
    <div class="title-section">
      <h2 class="smart-features-title">{{ section.settings.title | default: 'Smart Features' }}</h2>
      <p class="subtitle">{{ section.settings.subtitle | default: 'Experience intelligent 3D printing with advanced monitoring, control, and automation features designed for professional workflows.' }}</p>
    </div>

    <!-- Cards Carousel -->
    <div class="cards-carousel">
      <div class="cards-viewport">
        <div class="cards-container">

          <!-- Card 1: WiFi Remote Control -->
          <div class="feature-card active" data-card="0">
            <div class="card-video-section">
              <div class="card-video-bg">
                <video muted loop playsinline preload="metadata">
                  <source src="{{ section.settings.wifi_video | default: 'https://cdn.shopify.com/videos/c/o/v/9f390c272af443f286795902890c966d.mp4' }}" type="video/mp4">
                </video>
                <div class="video-overlay"></div>
              </div>
            </div>
            <div class="card-content-section">
              <div class="card-content">
                <h3 class="card-title">{{ section.settings.wifi_title | default: 'WiFi & Camera Remote Control' }}</h3>
                <p class="card-description">{{ section.settings.wifi_description | default: 'Control your printer remotely via phone or computer. Built-in camera provides real-time monitoring for long periods without supervision.' }}</p>
                <ul class="card-features">
                  <li>Remote control via mobile app</li>
                  <li>Real-time camera monitoring</li>
                  <li>Long-term unattended printing</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Card 2: Touch Screen -->
          <div class="feature-card" data-card="1">
            <div class="card-video-section">
              <div class="card-video-bg">
                <video muted loop playsinline preload="metadata">
                  <source src="{{ section.settings.screen_video | default: 'https://cdn.shopify.com/videos/c/o/v/1783adb825da4254a0261fec17338928.mp4' }}" type="video/mp4">
                </video>
                <div class="video-overlay"></div>
              </div>
            </div>
            <div class="card-content-section">
              <div class="card-content">
                <h3 class="card-title">{{ section.settings.screen_title | default: '4.3-inch Capacitive Touch Screen' }}</h3>
                <p class="card-description">{{ section.settings.screen_description | default: 'Intuitive capacitive touch interface with LED backlight for night visibility. Check printing status anytime with responsive touch controls.' }}</p>
                <ul class="card-features">
                  <li>4.3-inch capacitive touch display</li>
                  <li>LED backlight for night use</li>
                  <li>Intuitive status monitoring</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Card 3: Filament Detection -->
          <div class="feature-card" data-card="2">
            <div class="card-video-section">
              <div class="card-video-bg">
                <video muted loop playsinline preload="metadata">
                  <source src="{{ section.settings.filament_video | default: 'https://cdn.shopify.com/videos/c/o/v/a9ba3e535a934dc7a2257bcf3e5608db.mp4' }}" type="video/mp4">
                </video>
                <div class="video-overlay"></div>
              </div>
            </div>
            <div class="card-content-section">
              <div class="card-content">
                <h3 class="card-title">{{ section.settings.filament_title | default: 'Filament Detection System' }}</h3>
                <p class="card-description">{{ section.settings.filament_description | default: 'Advanced 12mm detection system automatically pauses when filament stops feeding. Resume printing seamlessly after reloading filament.' }}</p>
                <ul class="card-features">
                  <li>12mm detection distance</li>
                  <li>Automatic pause on filament break</li>
                  <li>Smart resume after reload</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Card 4: Camera Monitoring -->
          <div class="feature-card" data-card="3">
            <div class="card-video-section">
              <div class="card-video-bg">
                <video muted loop playsinline preload="metadata">
                  <source src="{{ section.settings.camera_video | default: 'https://cdn.shopify.com/videos/c/o/v/a90e296cfe5e45a68396afcd3abd1244.mp4' }}" type="video/mp4">
                </video>
                <div class="video-overlay"></div>
              </div>
            </div>
            <div class="card-content-section">
              <div class="card-content">
                <h3 class="card-title">{{ section.settings.camera_title | default: 'Built-in Camera Monitoring' }}</h3>
                <p class="card-description">{{ section.settings.camera_description | default: 'Integrated camera system provides continuous monitoring and recording. Watch your prints in real-time from anywhere in the world.' }}</p>
                <ul class="card-features">
                  <li>Built-in HD camera system</li>
                  <li>Real-time streaming</li>
                  <li>Remote monitoring capability</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Card 5: Collision Detection Sensor -->
          <div class="feature-card" data-card="4">
            <div class="card-video-section">
              <div class="card-video-bg">
                <video muted loop playsinline preload="metadata">
                  <source src="{{ section.settings.sensor_video | default: 'https://cdn.shopify.com/videos/c/o/v/55f6973d7a2b4f4b97be3cf55294b4db.mp4' }}" type="video/mp4">
                </video>
                <div class="video-overlay"></div>
              </div>
            </div>
            <div class="card-content-section">
              <div class="card-content">
                <h3 class="card-title">{{ section.settings.sensor_title | default: 'Collision Detection Sensor' }}</h3>
                <p class="card-description">{{ section.settings.sensor_description | default: 'Smart collision detection monitors nozzle position in real-time. When scratching exceeds 1mm depth, warning lights flash and printing stops automatically to prevent damage.' }}</p>
                <ul class="card-features">
                  <li>Real-time collision detection</li>
                  <li>Visual warning light system</li>
                  <li>Automatic print protection</li>
                </ul>
              </div>
            </div>
          </div>

        </div>
      </div>

      <!-- Navigation Controls -->
      <div class="navigation-controls">
        <button class="nav-arrow prev-arrow" aria-label="Previous feature">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M15 6L9 12L15 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>

        <button class="nav-arrow next-arrow" aria-label="Next feature">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M9 6L15 12L9 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>

      <!-- Progress Indicators - Moved below cards -->
      <div class="progress-indicators">
        <div class="indicator active" data-card="0"></div>
        <div class="indicator" data-card="1"></div>
        <div class="indicator" data-card="2"></div>
        <div class="indicator" data-card="3"></div>
        <div class="indicator" data-card="4"></div>
      </div>
    </div>

    <!-- Completion Indicator -->
    <div class="completion-indicator" style="display: none;">
      <div class="completion-icon">✓</div>
      <p class="completion-text">All features explored</p>
    </div>
  </div>
</section>



{% schema %}
{
  "name": "Smart Features",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Smart Features"
    },
    {
      "type": "textarea",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "Experience intelligent 3D printing with advanced monitoring, control, and automation features designed for professional workflows."
    },
    {
      "type": "header",
      "content": "Collision Detection Sensor Settings"
    },
    {
      "type": "video",
      "id": "sensor_video",
      "label": "Sensor Video",
      "info": "Video showing collision detection sensor in action"
    },
    {
      "type": "text",
      "id": "sensor_title",
      "label": "Sensor Card Title",
      "default": "Collision Detection Sensor"
    },
    {
      "type": "textarea",
      "id": "sensor_description",
      "label": "Sensor Card Description",
      "default": "Smart collision detection monitors nozzle position in real-time. When scratching exceeds 1mm depth, warning lights flash and printing stops automatically to prevent damage."
    }
  ],
  "presets": [
    {
      "name": "Smart Features"
    }
  ]
}
{% endschema %}
