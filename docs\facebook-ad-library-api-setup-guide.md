# Facebook Ad Library API 设置指南

## 概述

Facebook Ad Library API 允许开发者程序化访问Facebook广告库中的公开广告数据。本指南将详细说明如何设置和使用该API。

## 前置要求

### 1. Meta开发者账户
- 访问 [Meta for Developers](https://developers.facebook.com/)
- 使用您的Facebook账户登录
- 完成开发者账户注册

### 2. 政治广告身份验证（必需）
⚠️ **重要**: 即使您不打算投放政治广告，也必须完成此验证才能访问Ad Library API

**验证步骤：**
1. 访问 [Facebook身份验证页面](https://www.facebook.com/ID)
2. 点击"开始验证"
3. 提供以下信息：
   - 政府颁发的身份证件照片
   - 个人信息确认
   - 居住地址验证
4. 等待审核（通常需要1-3个工作日）

## API设置步骤

### 步骤1: 创建Facebook应用

1. 登录 [Meta for Developers](https://developers.facebook.com/)
2. 点击"我的应用" → "创建应用"
3. 选择应用类型：**其他** → **商业**
4. 填写应用信息：
   - 应用名称：例如 "Ad Library Research Tool"
   - 应用联系邮箱：您的邮箱地址
5. 点击"创建应用"

### 步骤2: 配置Facebook登录

1. 在应用仪表板中，点击"添加产品"
2. 找到"Facebook登录"，点击"设置"
3. 选择"Web"平台
4. 在"有效的OAuth重定向URI"中添加：
   ```
   https://developers.facebook.com/tools/explorer/
   ```
5. 保存更改

### 步骤3: 获取访问令牌

1. 访问 [Graph API Explorer](https://developers.facebook.com/tools/explorer/)
2. 在右上角选择您刚创建的应用
3. 点击"权限"标签
4. 搜索并添加权限：`ads_read`
5. 点击"生成访问令牌"
6. 在弹出窗口中登录并授权
7. 复制生成的访问令牌

### 步骤4: 测试API访问

使用以下URL测试您的访问令牌：
```
https://graph.facebook.com/v21.0/ads_archive?access_token=YOUR_ACCESS_TOKEN&search_terms=test&ad_reached_countries=["US"]&fields=id,page_name&limit=1
```

## 代码配置

### 更新访问令牌

在 `project/ai_studio_code.py` 文件中，将 `YOUR_ACCESS_TOKEN_HERE` 替换为您的实际访问令牌：

```python
ACCESS_TOKEN = 'EAAxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'  # 您的访问令牌
```

### API参数说明

| 参数 | 说明 | 示例值 |
|------|------|--------|
| `search_terms` | 搜索关键词 | `'Snapmaker U1'` |
| `ad_reached_countries` | 目标国家列表 | `['US', 'CA', 'GB']` |
| `ad_type` | 广告类型 | `'ALL'`, `'POLITICAL_AND_ISSUE_ADS'` |
| `ad_active_status` | 广告状态 | `'ALL'`, `'ACTIVE'`, `'INACTIVE'` |
| `fields` | 返回字段 | 见下方字段列表 |
| `limit` | 返回数量限制 | `1-1000` |

### 可用字段列表

**基础字段：**
- `id` - 广告ID
- `ad_creation_time` - 广告创建时间
- `ad_delivery_start_time` - 投放开始时间
- `ad_delivery_stop_time` - 投放结束时间
- `page_id` - 页面ID
- `page_name` - 页面名称
- `publisher_platforms` - 投放平台

**创意字段：**
- `ad_creative_bodies` - 广告文案
- `ad_creative_link_titles` - 链接标题
- `ad_creative_link_descriptions` - 链接描述
- `ad_snapshot_url` - 广告快照URL

**其他字段：**
- `languages` - 语言
- `currency` - 货币（仅政治广告）
- `spend` - 花费（仅政治广告）
- `impressions` - 展示次数（仅政治广告）

## 常见错误及解决方案

### 错误1: "Application does not have permission for this action"
**原因**: 未完成政治广告身份验证
**解决方案**: 完成Facebook身份验证流程

### 错误2: "Invalid OAuth access token"
**原因**: 访问令牌过期或无效
**解决方案**: 重新生成访问令牌

### 错误3: "Unsupported get request"
**原因**: API参数格式错误
**解决方案**: 检查参数格式，确保使用正确的字段名

### 错误4: "Rate limit exceeded"
**原因**: 请求频率过高
**解决方案**: 添加请求间隔，使用分页获取数据

## 最佳实践

### 1. 访问令牌管理
- 定期更新访问令牌
- 不要在代码中硬编码令牌
- 使用环境变量存储敏感信息

### 2. 请求优化
- 使用分页获取大量数据
- 只请求需要的字段
- 添加适当的请求间隔

### 3. 数据处理
- 处理空值和缺失字段
- 验证数据完整性
- 实现错误重试机制

## 示例代码

### 基础查询
```python
import requests

def search_ads(search_term, country='US', limit=25):
    url = 'https://graph.facebook.com/v21.0/ads_archive'
    params = {
        'access_token': ACCESS_TOKEN,
        'search_terms': search_term,
        'ad_reached_countries': [country],
        'fields': 'id,page_name,ad_creative_bodies,ad_snapshot_url',
        'limit': limit
    }
    
    response = requests.get(url, params=params)
    response.raise_for_status()
    return response.json()
```

### 分页获取数据
```python
def get_all_ads(search_term, country='US'):
    all_ads = []
    url = 'https://graph.facebook.com/v21.0/ads_archive'
    params = {
        'access_token': ACCESS_TOKEN,
        'search_terms': search_term,
        'ad_reached_countries': [country],
        'fields': 'id,page_name,ad_creative_bodies',
        'limit': 1000
    }
    
    while url:
        response = requests.get(url, params=params)
        response.raise_for_status()
        data = response.json()
        
        all_ads.extend(data.get('data', []))
        
        # 获取下一页URL
        url = data.get('paging', {}).get('next')
        params = None  # 下一页URL已包含所有参数
    
    return all_ads
```

## 技术支持

如果遇到问题，可以参考以下资源：
- [Facebook Ad Library API官方文档](https://developers.facebook.com/docs/marketing-api/audiences/reference/ad-library)
- [Meta开发者社区](https://developers.facebook.com/community/)
- [Graph API Explorer](https://developers.facebook.com/tools/explorer/)

## 更新日志

- **2025-01-04**: 更新至API v21.0，修正字段名称
- **2024-12-01**: 添加政治广告验证要求说明
- **2024-11-01**: 初始版本创建
