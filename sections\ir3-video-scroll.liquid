{{ 'ir3-video-scroll.css' | asset_url | stylesheet_tag }}

<!-- IR3 Video Scroll Component -->
<section class="ir3-video-scroll" id="ir3-video-scroll-{{ section.id }}" data-lazy-load="true">

  <!-- Placeholder Background -->
  <div class="video-placeholder">
    <div class="placeholder-blur-bg"></div>
    <div class="placeholder-content">
      <div class="placeholder-spinner"></div>
      <p class="placeholder-text">Loading video...</p>
    </div>
  </div>

  <!-- Loading State -->
  <div class="video-loading">
    <div class="loading-spinner"></div>
    <p>Loading video...</p>
  </div>

  <!-- Background Video -->
  <div class="video-container">
    <video
      class="background-video"
      muted
      playsinline
      preload="none"
      data-video-src="{% if section.settings.video_file != blank %}{{ section.settings.video_file }}{% elsif section.id contains 'performance' %}https://cdn.shopify.com/videos/c/o/v/3140e4cf7a464f64bf50bd66b6b47afd.mp4{% elsif section.id contains 'long_object' %}https://cdn.shopify.com/videos/c/o/v/ea6007175efd4be2909d0efba989a410.mp4{% else %}https://cdn.shopify.com/videos/c/o/v/17b1427672274de38bf28cd69b62fdf2.mp4{% endif %}"
    >
      Your browser does not support the video tag.
    </video>

    <!-- Video Overlay -->
    <div class="video-overlay" style="opacity: {{ section.settings.overlay_opacity | default: 0.3 }};"></div>
  </div>

  <!-- Content Container -->
  <div class="content-container">
    <div class="content-wrapper">
      {% if section.settings.title != blank %}
        <h2 class="video-title" style="color: {{ section.settings.text_color | default: '#ffffff' }};">
          {{ section.settings.title }}
        </h2>
      {% endif %}
      
      {% if section.settings.description != blank %}
        <p class="video-description" style="color: {{ section.settings.text_color | default: '#ffffff' }};">
          {{ section.settings.description }}
        </p>
      {% endif %}
    </div>
  </div>
</section>

{{ 'ir3-video-scroll.js' | asset_url | script_tag }}

{% schema %}
{
  "name": "IR3 Video Scroll",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Video Settings"
    },
    {
      "type": "video",
      "id": "video_file",
      "label": "Background Video",
      "info": "Upload a video file or use the default Klipper 3D printing video"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "min": 0,
      "max": 1,
      "step": 0.1,
      "label": "Video Overlay Opacity",
      "default": 0.3,
      "info": "Controls the darkness of the overlay on the video"
    },
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Klipper Firmware Excellence",
      "info": "Main heading displayed over the video"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description",
      "default": "Advanced Klipper firmware delivers unmatched precision and speed control, enabling professional-grade 3D printing with intelligent motion planning and real-time optimization.",
      "info": "Description text displayed below the title"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text Color",
      "default": "#ffffff",
      "info": "Color of the title and description text"
    }
  ],
  "presets": [
    {
      "name": "IR3 Video Scroll",
      "settings": {
        "title": "Klipper Firmware Excellence",
        "description": "Advanced Klipper firmware delivers unmatched precision and speed control, enabling professional-grade 3D printing with intelligent motion planning and real-time optimization.",
        "text_color": "#ffffff",
        "overlay_opacity": 0.3
      }
    }
  ]
}
{% endschema %}
