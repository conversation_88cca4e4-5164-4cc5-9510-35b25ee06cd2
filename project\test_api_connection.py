#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Facebook Ad Library API 连接测试脚本
用于验证API访问权限和基本功能
"""

import requests
import json
import sys

# 配置参数
ACCESS_TOKEN = 'EAAWZCgkp4ZAZA0BPR1yTAQReyNvUveOlYsZAeruDdFROeBF1Pxc0N8pXBKHYoPKhe7kGQoZA0ntsV1pkichoZBybDdB273sPpc7pdJPzvBByuXkI5N2zCyavSZBZBtU3Afd5JoO9qrJbZCDOCew4LmkZCQOVozhEblgZARJcMH6JNpqWvoTOQufrlSSmt2nIswMqZBZCiDt5wfkId'  # 请替换为您的访问令牌
API_VERSION = 'v21.0'

def test_api_connection():
    """测试API基本连接"""
    print("🔍 测试Facebook Ad Library API连接...")
    
    # 检查访问令牌
    if ACCESS_TOKEN == 'YOUR_ACCESS_TOKEN_HERE' or not ACCESS_TOKEN:
        print("❌ 错误：请先设置有效的访问令牌！")
        print("\n获取访问令牌的步骤：")
        print("1. 访问 https://developers.facebook.com/tools/explorer/")
        print("2. 选择您的应用")
        print("3. 添加权限：ads_read")
        print("4. 生成访问令牌")
        print("5. 将令牌复制到此脚本的ACCESS_TOKEN变量中")
        return False
    
    # 测试API访问
    url = f'https://graph.facebook.com/{API_VERSION}/ads_archive'
    params = {
        'access_token': ACCESS_TOKEN,
        'search_terms': 'test',
        'ad_reached_countries': ['US'],
        'fields': 'id,page_name',
        'limit': 1
    }
    
    try:
        print(f"📡 发送测试请求到: {url}")
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API连接成功！")
            print(f"📊 返回数据结构: {list(data.keys())}")
            
            if 'data' in data:
                ads_count = len(data['data'])
                print(f"📈 找到 {ads_count} 条测试广告")
                
                if ads_count > 0:
                    print("📋 示例广告数据:")
                    sample_ad = data['data'][0]
                    for key, value in sample_ad.items():
                        print(f"   {key}: {value}")
            
            return True
            
        else:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            print(f"📄 错误响应: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求错误: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
        return False

def test_specific_search():
    """测试特定关键词搜索"""
    print("\n🎯 测试Snapmaker U1广告搜索...")
    
    url = f'https://graph.facebook.com/{API_VERSION}/ads_archive'
    params = {
        'access_token': ACCESS_TOKEN,
        'search_terms': 'Snapmaker U1',
        'ad_reached_countries': ['US'],
        'ad_type': 'ALL',
        'ad_active_status': 'ALL',
        'fields': 'id,page_name,ad_creative_bodies,ad_snapshot_url,ad_delivery_start_time',
        'limit': 5,
        'search_type': 'KEYWORD_UNORDERED'
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            ads_found = data.get('data', [])
            
            if ads_found:
                print(f"✅ 找到 {len(ads_found)} 条Snapmaker U1广告")
                
                for i, ad in enumerate(ads_found, 1):
                    print(f"\n📱 广告 {i}:")
                    print(f"   ID: {ad.get('id', 'N/A')}")
                    print(f"   页面: {ad.get('page_name', 'N/A')}")
                    
                    # 处理广告文案（可能是数组）
                    bodies = ad.get('ad_creative_bodies', [])
                    if bodies:
                        body_text = bodies[0] if isinstance(bodies, list) else str(bodies)
                        print(f"   文案: {body_text[:100]}...")
                    
                    print(f"   快照: {ad.get('ad_snapshot_url', 'N/A')}")
                    print(f"   开始时间: {ad.get('ad_delivery_start_time', 'N/A')}")
            else:
                print("❌ 没有找到Snapmaker U1相关广告")
                print("可能的原因：")
                print("1. 该产品在美国市场没有投放广告")
                print("2. 广告已经停止投放")
                print("3. 搜索关键词不匹配")
                
        else:
            print(f"❌ 搜索请求失败: HTTP {response.status_code}")
            print(f"📄 错误详情: {response.text}")
            
    except Exception as e:
        print(f"❌ 搜索测试失败: {e}")

def test_available_fields():
    """测试可用字段"""
    print("\n📋 测试API可用字段...")
    
    # 常用字段列表
    test_fields = [
        'id',
        'ad_creation_time',
        'ad_creative_bodies',
        'ad_creative_link_titles',
        'ad_creative_link_descriptions',
        'ad_delivery_start_time',
        'ad_delivery_stop_time',
        'ad_snapshot_url',
        'page_id',
        'page_name',
        'publisher_platforms',
        'languages'
    ]
    
    url = f'https://graph.facebook.com/{API_VERSION}/ads_archive'
    params = {
        'access_token': ACCESS_TOKEN,
        'search_terms': 'facebook',
        'ad_reached_countries': ['US'],
        'fields': ','.join(test_fields),
        'limit': 1
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            ads = data.get('data', [])
            
            if ads:
                print("✅ 字段测试成功！")
                print("📊 可用字段:")
                
                sample_ad = ads[0]
                for field in test_fields:
                    if field in sample_ad:
                        value = sample_ad[field]
                        if isinstance(value, list):
                            print(f"   ✓ {field}: [数组，{len(value)}个元素]")
                        elif isinstance(value, str) and len(value) > 50:
                            print(f"   ✓ {field}: {value[:50]}...")
                        else:
                            print(f"   ✓ {field}: {value}")
                    else:
                        print(f"   ✗ {field}: 不可用")
            else:
                print("⚠️ 字段测试：没有返回数据")
                
        else:
            print(f"❌ 字段测试失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 字段测试错误: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 Facebook Ad Library API 测试工具")
    print("=" * 60)
    
    # 基本连接测试
    if not test_api_connection():
        print("\n❌ 基本连接测试失败，请检查访问令牌和网络连接")
        sys.exit(1)
    
    # 特定搜索测试
    test_specific_search()
    
    # 字段测试
    test_available_fields()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("如果所有测试都通过，您可以开始使用主脚本进行数据抓取。")
    print("=" * 60)

if __name__ == "__main__":
    main()
