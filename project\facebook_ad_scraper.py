#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Facebook Ad Library 数据抓取和分析工具
支持批量抓取、数据分析和Markdown报告生成
"""

import requests
import json
import time
import csv
from datetime import datetime
from typing import List, Dict, Optional
import os

class FacebookAdScraper:
    """Facebook广告库数据抓取器"""
    
    def __init__(self, access_token: str, api_version: str = 'v21.0'):
        """
        初始化抓取器
        
        Args:
            access_token: Facebook访问令牌
            api_version: API版本
        """
        self.access_token = access_token
        self.api_version = api_version
        self.base_url = f'https://graph.facebook.com/{api_version}/ads_archive'
        
        # 验证访问令牌
        if not access_token or access_token == 'YOUR_ACCESS_TOKEN_HERE':
            raise ValueError("请设置有效的Facebook访问令牌")
    
    def search_ads(self, 
                   search_terms: str,
                   countries: List[str] = ['US'],
                   ad_type: str = 'ALL',
                   ad_active_status: str = 'ALL',
                   limit: int = 1000,
                   fields: Optional[List[str]] = None) -> List[Dict]:
        """
        搜索广告数据
        
        Args:
            search_terms: 搜索关键词
            countries: 目标国家列表
            ad_type: 广告类型
            ad_active_status: 广告状态
            limit: 每页返回数量
            fields: 返回字段列表
            
        Returns:
            广告数据列表
        """
        if fields is None:
            fields = [
                'id', 'ad_creation_time', 'ad_creative_bodies',
                'ad_creative_link_titles', 'ad_creative_link_descriptions',
                'ad_delivery_start_time', 'ad_delivery_stop_time',
                'ad_snapshot_url', 'page_id', 'page_name',
                'publisher_platforms', 'languages'
            ]
        
        params = {
            'access_token': self.access_token,
            'search_terms': search_terms,
            'ad_reached_countries': countries,
            'ad_type': ad_type,
            'ad_active_status': ad_active_status,
            'fields': ','.join(fields),
            'limit': min(limit, 1000),  # API限制最大1000
            'search_type': 'KEYWORD_UNORDERED'
        }
        
        all_ads = []
        url = self.base_url
        page_count = 0
        
        print(f"🔍 开始搜索广告: {search_terms}")
        print(f"📍 目标国家: {', '.join(countries)}")
        
        while url and page_count < 10:  # 限制最大页数防止无限循环
            try:
                print(f"📄 获取第 {page_count + 1} 页数据...")
                response = requests.get(url, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                ads = data.get('data', [])
                
                if not ads:
                    print("📭 没有更多数据")
                    break
                
                all_ads.extend(ads)
                print(f"✅ 获取到 {len(ads)} 条广告，累计 {len(all_ads)} 条")
                
                # 获取下一页URL
                paging = data.get('paging', {})
                url = paging.get('next')
                params = None  # 下一页URL已包含参数
                
                page_count += 1
                
                # 添加延迟避免触发限流
                time.sleep(1)
                
            except requests.exceptions.RequestException as e:
                print(f"❌ 请求错误: {e}")
                break
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析错误: {e}")
                break
        
        print(f"🎉 搜索完成！总共获取 {len(all_ads)} 条广告数据")
        return all_ads
    
    def analyze_ads(self, ads: List[Dict]) -> Dict:
        """
        分析广告数据
        
        Args:
            ads: 广告数据列表
            
        Returns:
            分析结果字典
        """
        if not ads:
            return {}
        
        print("📊 开始分析广告数据...")
        
        analysis = {
            'total_ads': len(ads),
            'pages': {},
            'platforms': {},
            'languages': {},
            'creation_dates': {},
            'active_ads': 0,
            'sample_ads': ads[:5]  # 保存前5个广告作为样本
        }
        
        for ad in ads:
            # 页面统计
            page_name = ad.get('page_name', 'Unknown')
            analysis['pages'][page_name] = analysis['pages'].get(page_name, 0) + 1
            
            # 平台统计
            platforms = ad.get('publisher_platforms', [])
            if isinstance(platforms, list):
                for platform in platforms:
                    analysis['platforms'][platform] = analysis['platforms'].get(platform, 0) + 1
            
            # 语言统计
            languages = ad.get('languages', [])
            if isinstance(languages, list):
                for lang in languages:
                    analysis['languages'][lang] = analysis['languages'].get(lang, 0) + 1
            
            # 创建日期统计
            creation_time = ad.get('ad_creation_time', '')
            if creation_time:
                date = creation_time.split('T')[0]  # 提取日期部分
                analysis['creation_dates'][date] = analysis['creation_dates'].get(date, 0) + 1
            
            # 活跃广告统计
            if not ad.get('ad_delivery_stop_time'):
                analysis['active_ads'] += 1
        
        print("✅ 数据分析完成")
        return analysis
    
    def save_to_csv(self, ads: List[Dict], filename: str):
        """
        保存数据到CSV文件
        
        Args:
            ads: 广告数据列表
            filename: 文件名
        """
        if not ads:
            print("❌ 没有数据可保存")
            return
        
        print(f"💾 保存数据到 {filename}...")
        
        # 获取所有字段
        all_fields = set()
        for ad in ads:
            all_fields.update(ad.keys())
        
        all_fields = sorted(list(all_fields))
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=all_fields)
            writer.writeheader()
            
            for ad in ads:
                # 处理列表字段
                row = {}
                for field in all_fields:
                    value = ad.get(field, '')
                    if isinstance(value, list):
                        row[field] = '; '.join(str(v) for v in value)
                    else:
                        row[field] = str(value) if value else ''
                writer.writerow(row)
        
        print(f"✅ 数据已保存到 {filename}")
    
    def generate_markdown_report(self, ads: List[Dict], analysis: Dict, filename: str):
        """
        生成Markdown分析报告
        
        Args:
            ads: 广告数据列表
            analysis: 分析结果
            filename: 文件名
        """
        print(f"📝 生成Markdown报告: {filename}...")
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("# Facebook广告库数据分析报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 概览
            f.write("## 数据概览\n\n")
            f.write(f"- **总广告数量**: {analysis.get('total_ads', 0)}\n")
            f.write(f"- **活跃广告数量**: {analysis.get('active_ads', 0)}\n")
            f.write(f"- **广告主数量**: {len(analysis.get('pages', {}))}\n")
            f.write(f"- **投放平台数量**: {len(analysis.get('platforms', {}))}\n\n")
            
            # 广告主排行
            f.write("## 广告主排行\n\n")
            f.write("| 排名 | 广告主 | 广告数量 |\n")
            f.write("|------|--------|----------|\n")
            
            pages = analysis.get('pages', {})
            sorted_pages = sorted(pages.items(), key=lambda x: x[1], reverse=True)
            for i, (page, count) in enumerate(sorted_pages[:10], 1):
                f.write(f"| {i} | {page} | {count} |\n")
            f.write("\n")
            
            # 平台分布
            f.write("## 投放平台分布\n\n")
            f.write("| 平台 | 广告数量 |\n")
            f.write("|------|----------|\n")
            
            platforms = analysis.get('platforms', {})
            sorted_platforms = sorted(platforms.items(), key=lambda x: x[1], reverse=True)
            for platform, count in sorted_platforms:
                f.write(f"| {platform} | {count} |\n")
            f.write("\n")
            
            # 语言分布
            if analysis.get('languages'):
                f.write("## 语言分布\n\n")
                f.write("| 语言 | 广告数量 |\n")
                f.write("|------|----------|\n")
                
                languages = analysis.get('languages', {})
                sorted_languages = sorted(languages.items(), key=lambda x: x[1], reverse=True)
                for lang, count in sorted_languages:
                    f.write(f"| {lang} | {count} |\n")
                f.write("\n")
            
            # 样本广告
            f.write("## 样本广告\n\n")
            sample_ads = analysis.get('sample_ads', [])
            for i, ad in enumerate(sample_ads, 1):
                f.write(f"### 广告 {i}\n\n")
                f.write(f"- **广告ID**: {ad.get('id', 'N/A')}\n")
                f.write(f"- **广告主**: {ad.get('page_name', 'N/A')}\n")
                
                # 广告文案
                bodies = ad.get('ad_creative_bodies', [])
                if bodies:
                    body = bodies[0] if isinstance(bodies, list) else str(bodies)
                    f.write(f"- **广告文案**: {body[:200]}...\n")
                
                # 广告标题
                titles = ad.get('ad_creative_link_titles', [])
                if titles:
                    title = titles[0] if isinstance(titles, list) else str(titles)
                    f.write(f"- **广告标题**: {title}\n")
                
                f.write(f"- **创建时间**: {ad.get('ad_creation_time', 'N/A')}\n")
                f.write(f"- **快照链接**: {ad.get('ad_snapshot_url', 'N/A')}\n\n")
        
        print(f"✅ 报告已生成: {filename}")

def main():
    """主函数"""
    # 配置参数
    ACCESS_TOKEN = 'YOUR_ACCESS_TOKEN_HERE'  # 请替换为您的访问令牌
    SEARCH_TERMS = 'Snapmaker U1'
    COUNTRIES = ['US', 'CA', 'GB', 'AU']  # 多个国家
    
    try:
        # 创建抓取器
        scraper = FacebookAdScraper(ACCESS_TOKEN)
        
        # 搜索广告
        ads = scraper.search_ads(
            search_terms=SEARCH_TERMS,
            countries=COUNTRIES,
            limit=500
        )
        
        if not ads:
            print("❌ 没有找到相关广告数据")
            return
        
        # 分析数据
        analysis = scraper.analyze_ads(ads)
        
        # 创建输出目录
        output_dir = 'output'
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        csv_filename = f"{output_dir}/facebook_ads_{timestamp}.csv"
        md_filename = f"{output_dir}/facebook_ads_report_{timestamp}.md"
        
        # 保存数据
        scraper.save_to_csv(ads, csv_filename)
        scraper.generate_markdown_report(ads, analysis, md_filename)
        
        print("\n🎉 数据抓取和分析完成！")
        print(f"📊 CSV数据文件: {csv_filename}")
        print(f"📝 分析报告: {md_filename}")
        
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
        print("请在代码中设置有效的ACCESS_TOKEN")
    except Exception as e:
        print(f"❌ 程序执行错误: {e}")

if __name__ == "__main__":
    main()
