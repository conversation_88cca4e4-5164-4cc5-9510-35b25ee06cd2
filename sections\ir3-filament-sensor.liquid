{% comment %}
  IR3 Filament Sensor Section - Smart Detection Features
  File: sections/ir3-filament-sensor.liquid
{% endcomment %}

{{ 'ir3-filament-sensor.css' | asset_url | stylesheet_tag }}

<section
  class="filament-sensor-section"
  id="filament-sensor-{{ section.id }}"
  data-section-id="{{ section.id }}"
  style="margin-top: {{ section.settings.margin_top }}px; margin-bottom: {{ section.settings.margin_bottom }}px;"
>
  <!-- Background Video Layer -->
  <div class="sensor-background">
    <!-- Canvas容器 - 模仿ir3-v2-auto-leveling-frames结构 -->
    <div class="sensor-canvas-container" id="sensor-canvas-container-{{ section.id }}">
      <canvas
        class="sensor-canvas"
        id="sensor-canvas-{{ section.id }}"
        data-frame-count="{{ section.settings.frame_count | default: 45 }}"
        data-frame-base-url="{{ section.settings.frame_base_url | default: 'https://cdn.shopify.com/s/files/1/0762/6113/0493/files/' }}"
        data-frame-prefix="{{ section.settings.frame_prefix | default: 'jlf27qss_' }}"
        data-frame-suffix="{{ section.settings.frame_suffix | default: '.webp?v=1755334753' }}"
      ></canvas>
    </div>
    <!-- 视频遮罩层 -->
    <div class="video-overlay"></div>
  </div>

  <!-- Content Container -->
  <div class="sensor-content-container">
    <!-- Text Content with Dynamic Switching -->
    <div class="text-content">
      <!-- 第一段文案 -->
      <div class="text-section" id="text-section-1-{{ section.id }}">
        <h2 class="section-title">{{ section.settings.main_title | default: 'Smart Filament Detection' }}</h2>
        <p class="section-description">{{ section.settings.main_description | default: 'Intelligent filament detection system that pauses printing when filament flow stops for over 12mm. Automatically detects filament runout, clogs, or feeding issues to prevent print failures.' }}</p>
      </div>
    </div>
  </div>
</section>

<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js" defer></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js" defer></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const sensorSection = document.getElementById('filament-sensor-{{ section.id }}');
  if (!sensorSection) return;

  // 等待GSAP加载
  function waitForGSAP() {
    return new Promise((resolve) => {
      if (typeof gsap !== 'undefined' && typeof ScrollTrigger !== 'undefined') {
        resolve();
      } else {
        setTimeout(() => waitForGSAP().then(resolve), 100);
      }
    });
  }

  waitForGSAP().then(() => {
    // 注册ScrollTrigger插件
    gsap.registerPlugin(ScrollTrigger);

    console.log('=== IR3 Filament Sensor Section Initialized ===');

    // 获取canvas容器和canvas元素
    const canvasContainer = sensorSection.querySelector('.sensor-canvas-container');
    const canvas = sensorSection.querySelector('.sensor-canvas');
    if (!canvas || !canvasContainer) {
      console.error('Canvas elements not found:', { canvas: !!canvas, container: !!canvasContainer });
      return;
    }

    // 帧序列配置
    const frameCount = parseInt(canvas.getAttribute('data-frame-count')) || 244;
    const frameBaseUrl = canvas.getAttribute('data-frame-base-url') || '';
    const framePrefix = canvas.getAttribute('data-frame-prefix') || 'frame_';
    const frameSuffix = canvas.getAttribute('data-frame-suffix') || '.webp';

    console.log('Frame config:', { frameCount, frameBaseUrl, framePrefix, frameSuffix });

    // 🔍 立即测试第一帧图片URL（根据设备类型）
    const isMobileTest = window.innerWidth <= 768;
    const testPrefix = isMobileTest ? 'jhozi93x_' : framePrefix;
    const testSuffix = isMobileTest ? '.webp?v=1755483135' : frameSuffix;
    const testFirstFrameUrl = `${frameBaseUrl}${testPrefix}0001${testSuffix}`;

    console.log('🧪 Testing first frame URL:', testFirstFrameUrl, isMobileTest ? '(Mobile)' : '(Desktop)');

    const testImg = new Image();
    testImg.onload = () => {
      console.log('✅ First frame URL is accessible:', testFirstFrameUrl);
      console.log('✅ Image dimensions:', testImg.width, 'x', testImg.height);
    };
    testImg.onerror = (error) => {
      console.error('❌ First frame URL failed:', testFirstFrameUrl, error);
    };
    testImg.src = testFirstFrameUrl;

    // 完整的帧序列播放器（参考ir3-v2-auto-leveling-frames.liquid）
    class FrameSequencePlayer {
      constructor(canvas, canvasContainer, frameCount, baseUrl, prefix, suffix) {
        this.canvas = canvas;
        this.canvasContainer = canvasContainer;
        this.ctx = canvas.getContext('2d');
        this.frameCount = frameCount;
        this.baseUrl = baseUrl;
        this.prefix = prefix;
        this.suffix = suffix;
        this.frames = [];
        this.currentFrame = 0;
        this.isLoaded = false;

        this.setupCanvas();
        this.loadFrames();
      }

      setupCanvas() {
        // 检测是否为移动端
        this.isMobile = window.innerWidth <= 768;

        console.log('🖼️ Setting up Canvas:', {
          isMobile: this.isMobile,
          screenSize: { width: window.innerWidth, height: window.innerHeight },
          canvasElement: !!this.canvas,
          context: !!this.ctx
        });

        if (this.isMobile) {
          // 移动端：先设置基本属性，尺寸将在第一帧加载后设置
          const dpr = window.devicePixelRatio || 1;
          this.dpr = dpr;

          // 临时设置Canvas的CSS尺寸，避免零尺寸错误
          this.canvas.style.width = window.innerWidth + 'px';
          this.canvas.style.height = window.innerHeight + 'px';
          this.canvas.width = window.innerWidth * dpr;
          this.canvas.height = window.innerHeight * dpr;
          this.ctx.scale(dpr, dpr);

          console.log('📱 Mobile setup: waiting for first frame to determine aspect ratio');
        } else {
          // 桌面端：保持原有逻辑
          const rect = this.canvas.getBoundingClientRect();
          const dpr = window.devicePixelRatio || 1;

          if (rect.width === 0 || rect.height === 0) {
            console.error('❌ Canvas has zero dimensions!', rect);
            return;
          }

          this.canvas.width = rect.width * dpr;
          this.canvas.height = rect.height * dpr;
          this.canvas.style.width = rect.width + 'px';
          this.canvas.style.height = rect.height + 'px';

          this.ctx.scale(dpr, dpr);
          console.log('🖥️ Desktop setup complete');
        }
      }

      setupMobileCanvas(imageAspectRatio) {
        if (!this.isMobile) return;

        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;
        const viewportAspectRatio = viewportWidth / viewportHeight;

        console.log('📱 Setting up mobile Canvas:', {
          imageAspectRatio: imageAspectRatio,
          viewportAspectRatio: viewportAspectRatio,
          viewportSize: { width: viewportWidth, height: viewportHeight }
        });

        // 移动端始终以高度为准（100vh），宽度按比例计算，允许溢出
        const canvasHeight = viewportHeight; // 始终100vh
        const canvasWidth = canvasHeight * imageAspectRatio; // 按比例计算宽度

        console.log('📱 Mobile sizing: height=100vh, width calculated by aspect ratio:', {
          canvasHeight: canvasHeight,
          canvasWidth: canvasWidth,
          willOverflow: canvasWidth > viewportWidth
        });

        // 设置Canvas的CSS尺寸（显示尺寸）
        this.canvas.style.width = canvasWidth + 'px';
        this.canvas.style.height = canvasHeight + 'px';

        // 设置Canvas的内部分辨率（用于绘制）
        this.canvas.width = canvasWidth * this.dpr;
        this.canvas.height = canvasHeight * this.dpr;

        // 重新设置上下文缩放
        this.ctx.setTransform(1, 0, 0, 1, 0, 0); // 重置变换
        this.ctx.scale(this.dpr, this.dpr);

        console.log('✅ Mobile Canvas setup complete:', {
          cssSize: { width: canvasWidth, height: canvasHeight },
          internalSize: { width: this.canvas.width, height: this.canvas.height },
          willOverflow: canvasWidth > viewportWidth,
          dpr: this.dpr
        });
      }

      async loadFrames() {
        console.log(`🔄 Starting to load ${this.frameCount} frames...`);

        // 根据设备类型选择不同的素材
        let actualPrefix = this.prefix;
        let actualSuffix = this.suffix;

        if (this.isMobile) {
          // 移动端使用专门的素材
          actualPrefix = 'jhozi93x_';
          actualSuffix = '.webp?v=1755483135';
          console.log('📱 Using mobile-optimized frames:', actualPrefix);
        }

        // 批量加载所有帧
        const batchSize = 10;
        const batches = [];

        for (let i = 0; i < this.frameCount; i += batchSize) {
          const batch = [];
          for (let j = i; j < Math.min(i + batchSize, this.frameCount); j++) {
            const frameNumber = j + 1;
            const frameUrl = `${this.baseUrl}${actualPrefix}${String(frameNumber).padStart(4, '0')}${actualSuffix}`;
            batch.push(this.loadFrame(j, frameUrl));
          }
          batches.push(Promise.all(batch));
        }

        try {
          // 加载第一批帧
          console.log('🔄 Loading first batch of frames...');
          await batches[0];
          console.log('✅ First batch loaded');

          // 确保第一帧已加载并绘制
          if (this.frames[0]) {
            console.log('🎨 Drawing initial frame after first batch load');
            this.drawFrame(0);

            // 🎯 关键：显示Canvas容器
            this.canvasContainer.classList.add('loaded');
            console.log('✅ Canvas container made visible');
          }

          // 继续加载其余帧
          for (let i = 1; i < batches.length; i++) {
            await batches[i];
            console.log(`✅ Batch ${i + 1}/${batches.length} loaded`);
          }

          this.isLoaded = true;
          console.log('🎉 All frames loaded successfully');

          // 触发帧加载完成事件
          document.dispatchEvent(new CustomEvent('sensorFramesLoaded', {
            detail: { player: this }
          }));

        } catch (error) {
          console.error('❌ Frame loading failed:', error);

          // 即使加载失败，也尝试使用已加载的帧
          if (this.frames.length > 0) {
            console.log('⚠️ Using partially loaded frames');
            this.canvasContainer.classList.add('loaded');
            document.dispatchEvent(new CustomEvent('sensorFramesLoaded', {
              detail: { player: this }
            }));
          }
        }
      }

      loadFrame(index, url) {
        return new Promise((resolve) => {
          const img = new Image();
          img.crossOrigin = 'anonymous';

          img.onload = () => {
            this.frames[index] = img;
            console.log(`✅ Frame ${index + 1} loaded successfully - Size: ${img.width}x${img.height}`);

            // 如果是第一帧，处理移动端适配
            if (index === 0) {
              // 获取图片的宽高比
              const imageAspectRatio = img.width / img.height;
              console.log(`📐 First frame aspect ratio: ${imageAspectRatio.toFixed(2)} (${img.width}x${img.height})`);

              // 如果是移动端，重新设置Canvas尺寸
              if (this.isMobile) {
                // 🔧 修复时序问题：立即显示Canvas容器
                this.canvasContainer.classList.add('loaded');
                console.log('📱 Mobile: Canvas container made visible for first frame');

                this.setupMobileCanvas(imageAspectRatio);
                // 等待Canvas设置完成后再绘制
                setTimeout(() => {
                  console.log('🎨 Drawing first frame after mobile setup');
                  this.drawFrame(0);
                }, 100);
              } else {
                // 桌面端直接绘制
                console.log('🎨 Drawing first frame');
                this.drawFrame(0);
              }
            }

            resolve();
          };

          img.onerror = (error) => {
            console.error(`❌ Failed to load frame ${index + 1}: ${url}`, error);
            console.error(`❌ Error details:`, error.type, error.target);

            // 尝试测试URL是否可访问
            fetch(url, { method: 'HEAD' })
              .then(response => {
                console.log(`🌐 URL test for frame ${index + 1}: Status ${response.status}`);
              })
              .catch(fetchError => {
                console.error(`🌐 URL test failed for frame ${index + 1}:`, fetchError);
              });

            resolve(); // 继续加载其他帧
          };

          console.log(`🔄 Loading frame ${index + 1}: ${url}`);
          img.src = url;
        });
      }

      drawFrame(frameIndex) {
        // 获取Canvas的实际CSS尺寸进行绘制
        let drawWidth, drawHeight;

        if (this.isMobile) {
          // 移动端：使用Canvas的实际CSS尺寸
          drawWidth = parseInt(this.canvas.style.width) || window.innerWidth;
          drawHeight = parseInt(this.canvas.style.height) || window.innerHeight;
        } else {
          // 桌面端：使用getBoundingClientRect
          const rect = this.canvas.getBoundingClientRect();
          drawWidth = rect.width;
          drawHeight = rect.height;
        }

        // 🔍 添加Canvas容器可见性检查
        if (frameIndex === 0) {
          const containerOpacity = window.getComputedStyle(this.canvasContainer).opacity;
          const containerVisible = this.canvasContainer.classList.contains('loaded');
          console.log('🔍 Canvas container status:', {
            opacity: containerOpacity,
            hasLoadedClass: containerVisible,
            canvasSize: { width: drawWidth, height: drawHeight }
          });
        }

        this.ctx.clearRect(0, 0, drawWidth, drawHeight);

        if (this.frames[frameIndex]) {
          try {
            const img = this.frames[frameIndex];
            this.ctx.drawImage(img, 0, 0, drawWidth, drawHeight);

            // 只在关键时刻输出日志
            if (frameIndex === 0 || frameIndex % 50 === 0) {
              console.log(`✅ Drew frame ${frameIndex + 1} - Canvas: ${drawWidth}x${drawHeight}, Image: ${img.width}x${img.height}`);
            }
          } catch (error) {
            console.error(`❌ Failed to draw frame ${frameIndex + 1}:`, error);
            this.drawErrorPattern(drawWidth, drawHeight, `Frame ${frameIndex + 1} Error`);
          }
        } else {
          console.warn(`⚠️ Frame ${frameIndex + 1} not available`);
          // 如果帧还没加载，显示第一帧作为占位符
          if (this.frames[0]) {
            const img = this.frames[0];
            this.ctx.drawImage(img, 0, 0, drawWidth, drawHeight);
            console.log(`🔄 Using frame 1 as placeholder for frame ${frameIndex + 1}`);
          } else {
            this.drawErrorPattern(drawWidth, drawHeight, `Loading Frame ${frameIndex + 1}...`);
          }
        }
      }

      drawErrorPattern(width, height, message) {
        this.ctx.fillStyle = '#333333';
        this.ctx.fillRect(0, 0, width, height);
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '24px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(message, width / 2, height / 2);
      }

      setFrame(frameIndex) {
        const clampedIndex = Math.max(0, Math.min(frameIndex, this.frameCount - 1));
        if (clampedIndex !== this.currentFrame) {
          this.currentFrame = clampedIndex;
          this.drawFrame(clampedIndex);
        }
      }

      getProgress() {
        return this.currentFrame / (this.frameCount - 1);
      }
    }

    // 创建帧播放器
    const framePlayer = new FrameSequencePlayer(canvas, canvasContainer, frameCount, frameBaseUrl, framePrefix, frameSuffix);

    // 等待帧加载完成后初始化ScrollTrigger
    document.addEventListener('sensorFramesLoaded', function(event) {
      console.log('🚀 Frames loaded, initializing ScrollTrigger...');

      // 获取文案元素
      const textSection1 = sensorSection.querySelector('#text-section-1-{{ section.id }}');

      // 初始状态：文案隐藏，等待入场动画
      // 不立即添加active类，让ScrollTrigger控制入场

      // 创建ScrollTrigger动画（参考scroll.html）
      const timeline = gsap.timeline({
        scrollTrigger: {
          trigger: sensorSection,
          pin: true,
          start: "top top",
          end: "+=3000", // 3000px的滚动距离
          scrub: 1,
          onUpdate: (self) => {
            // 根据滚动进度更新帧
            const progress = self.progress;
            const frameIndex = Math.floor(progress * (frameCount - 1));
            framePlayer.setFrame(frameIndex);

            // 文案显示逻辑：
            // 0-15%：无文案显示
            // 15-100%：显示第一段文案
            if (progress < 0.15) {
              // 组件刚进入，暂不显示文案
              textSection1.classList.remove('active');
            } else {
              // 15%后显示第一段文案
              if (!textSection1.classList.contains('active')) {
                textSection1.classList.add('active');
              }
            }

            // 减少日志频率，只在帧变化时输出
            if (frameIndex !== framePlayer.lastLoggedFrame) {
              console.log(`📊 Scroll progress: ${(progress * 100).toFixed(1)}%, frame: ${frameIndex + 1}/${frameCount}`);
              framePlayer.lastLoggedFrame = frameIndex;
            }
          },
          onEnter: () => {
            console.log('🎯 Entered Filament Sensor section');
            // 移除立即显示文案的逻辑，改由onUpdate中的进度控制
          },
          onLeave: () => {
            console.log('👋 Left Filament Sensor section');
          }
        }
      });

      console.log('✅ ScrollTrigger initialized for Filament Sensor');
    });

    console.log('Filament Sensor initialization completed');
  });
});
</script>

{% schema %}
{
  "name": "IR3 Filament Sensor",
  "tag": "section",
  "class": "ir3-filament-sensor-section",
  "settings": [
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "text",
      "id": "main_title",
      "label": "Main Title",
      "default": "Smart Filament Detection"
    },
    {
      "type": "textarea",
      "id": "main_description",
      "label": "Main Description",
      "default": "Intelligent filament detection system that pauses printing when filament flow stops for over 12mm. Automatically detects filament runout, clogs, or feeding issues to prevent print failures."
    },
    {
      "type": "header",
      "content": "Frame Sequence Settings"
    },
    {
      "type": "number",
      "id": "frame_count",
      "label": "Frame Count",
      "default": 45
    },
    {
      "type": "text",
      "id": "frame_base_url",
      "label": "Frame Base URL",
      "default": "https://cdn.shopify.com/s/files/1/0762/6113/0493/files/"
    },
    {
      "type": "text",
      "id": "frame_prefix",
      "label": "Frame Prefix",
      "default": "jlf27qss_"
    },
    {
      "type": "text",
      "id": "frame_suffix",
      "label": "Frame Suffix",
      "default": ".webp?v=1755334753"
    },
    {
      "type": "header",
      "content": "Spacing Settings"
    },
    {
      "type": "number",
      "id": "margin_top",
      "label": "Top Margin (px)",
      "default": 0
    },
    {
      "type": "number",
      "id": "margin_bottom",
      "label": "Bottom Margin (px)",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "IR3 Filament Sensor"
    }
  ]
}
{% endschema %}
