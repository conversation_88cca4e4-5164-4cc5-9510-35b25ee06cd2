# Shopify 国际化实施方案

## 📋 项目现状分析

### 当前多语言支持情况

**✅ 已有的语言文件：**
- `locales/en.default.json` - 英语（默认）
- `locales/es.json` - 西班牙语
- `locales/fr.json` - 法语
- `locales/de.json` - 德语
- `locales/it.json` - 意大利语
- `locales/pt-BR.json` - 巴西葡萄牙语
- `locales/pt-PT.json` - 葡萄牙语

**✅ 已有的语言选择器：**
- Footer中已实现语言选择器（`sections/footer.liquid` 第67-90行）
- 使用Shopify原生的 `localization` 表单
- 支持自动检测可用语言

**❌ 缺少的功能：**
- Header中没有语言选择器
- 主页没有显著的语言选择入口
- 缺少中文、日语、韩语等亚洲语言支持

## 🎯 实施目标
  
1. **在Header添加语言选择器** - 提供更显著的语言切换入口
2. **扩展语言支持** - 添加中文、日语、韩语等主要市场语言
3. **优化用户体验** - 智能语言检测和推荐
4. **完善翻译内容** - 确保所有界面元素都有对应翻译

## 🚀 实施方案

### 阶段一：Header语言选择器实现

#### 1.1 修改Header组件
**文件：** `sections/header-tech-dark.liquid`

在导航栏右侧添加语言选择器，位置在搜索和购物车图标之前：

```liquid
<!-- 在第1150行左右，购物车图标之前添加 -->
<!-- 语言选择器 -->
<div class="tech-nav-item tech-language-selector">
  {%- form 'localization', class: 'tech-localization-form', data-disclosure-form: '' -%}
    <div class="tech-disclosure">
      <button type="button" class="tech-language-toggle" aria-expanded="false" data-disclosure-toggle>
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/>
        </svg>
        <span class="tech-language-text">{{ form.current_locale.endonym_name | capitalize }}</span>
        <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
          <path d="M3 5L6 8L9 5" stroke="currentColor" stroke-width="1.5" fill="none"/>
        </svg>
      </button>
      <ul class="tech-language-list" data-disclosure-list>
        {%- for locale in form.available_locales -%}
          <li class="tech-language-item {% if locale.iso_code == form.current_locale.iso_code %}tech-language-item--current{% endif %}">
            <a class="tech-language-option" href="#" lang="{{ locale.iso_code }}" 
               {% if locale.iso_code == form.current_locale.iso_code %}aria-current="true"{% endif %} 
               data-value="{{ locale.iso_code }}" data-disclosure-option>
              <span class="tech-language-flag">{{ locale.iso_code | upcase }}</span>
              <span class="tech-language-name">{{ locale.endonym_name | capitalize }}</span>
            </a>
          </li>
        {%- endfor -%}
      </ul>
      <input type="hidden" name="locale_code" value="{{ form.current_locale.iso_code }}" data-disclosure-input/>
    </div>
  {%- endform -%}
</div>
```

#### 1.2 添加CSS样式
在 `sections/header-tech-dark.liquid` 的 `<style>` 部分添加：

```css
/* 语言选择器样式 */
.tech-language-selector {
  position: relative;
  margin-right: 20px;
}

.tech-language-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--tech-border);
  border-radius: 8px;
  padding: 8px 12px;
  color: var(--tech-text);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tech-language-toggle:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--tech-primary);
}

.tech-language-list {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--tech-bg-darker);
  border: 1px solid var(--tech-border);
  border-radius: 8px;
  min-width: 200px;
  max-height: 300px;
  overflow-y: auto;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
  backdrop-filter: blur(var(--tech-glass-blur));
}

.tech-disclosure[aria-expanded="true"] .tech-language-list {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.tech-language-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  color: var(--tech-text);
  text-decoration: none;
  transition: all 0.2s ease;
}

.tech-language-option:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--tech-primary);
}

.tech-language-item--current .tech-language-option {
  background: rgba(var(--tech-primary-rgb), 0.2);
  color: var(--tech-primary);
}

.tech-language-flag {
  font-size: 12px;
  font-weight: 600;
  opacity: 0.8;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .tech-language-selector {
    margin-right: 10px;
  }
  
  .tech-language-toggle {
    padding: 6px 8px;
    font-size: 12px;
  }
  
  .tech-language-text {
    display: none;
  }
}
```

### 阶段二：扩展语言支持

#### 2.1 添加亚洲语言文件

需要创建以下语言文件：

**中文简体 (`locales/zh-CN.json`)：**
```json
{
  "general": {
    "404": {
      "title": "404 页面未找到",
      "subtext_html": "<p>您要查找的页面不存在。</p><p><a href='{{ url }}'>继续购物</a></p>"
    },
    "language": {
      "dropdown_label": "语言"
    },
    "currency": {
      "dropdown_label": "货币"
    }
  },
  "layout": {
    "cart": {
      "title": "购物车"
    },
    "customer": {
      "account": "账户",
      "log_out": "退出登录",
      "log_in": "登录",
      "create_account": "创建账户"
    }
  },
  "products": {
    "product": {
      "sold_out": "售罄",
      "unavailable": "不可用",
      "add_to_cart": "加入购物车",
      "price": "价格"
    }
  }
}
```

**日语 (`locales/ja.json`)：**
```json
{
  "general": {
    "404": {
      "title": "404 ページが見つかりません",
      "subtext_html": "<p>お探しのページは存在しません。</p><p><a href='{{ url }}'>ショッピングを続ける</a></p>"
    },
    "language": {
      "dropdown_label": "言語"
    }
  },
  "layout": {
    "cart": {
      "title": "カート"
    },
    "customer": {
      "account": "アカウント",
      "log_out": "ログアウト",
      "log_in": "ログイン",
      "create_account": "アカウント作成"
    }
  }
}
```

#### 2.2 Shopify后台配置

在Shopify后台需要启用这些语言：
1. 进入 **Settings → Languages**
2. 点击 **Add language**
3. 选择要添加的语言
4. 上传对应的语言文件

### 阶段三：智能语言检测

#### 3.1 创建语言检测脚本
**文件：** `assets/language-detector.js`

```javascript
(function() {
  'use strict';
  
  const LanguageDetector = {
    // 支持的语言映射
    supportedLanguages: {
      'en': 'en',
      'es': 'es', 
      'fr': 'fr',
      'de': 'de',
      'it': 'it',
      'pt': 'pt-PT',
      'zh': 'zh-CN',
      'ja': 'ja',
      'ko': 'ko'
    },
    
    // 检测用户首选语言
    detectUserLanguage: function() {
      // 1. 检查URL参数
      const urlParams = new URLSearchParams(window.location.search);
      const urlLang = urlParams.get('lang');
      if (urlLang && this.supportedLanguages[urlLang]) {
        return this.supportedLanguages[urlLang];
      }
      
      // 2. 检查localStorage
      const savedLang = localStorage.getItem('preferred_language');
      if (savedLang && this.supportedLanguages[savedLang]) {
        return this.supportedLanguages[savedLang];
      }
      
      // 3. 检查浏览器语言
      const browserLang = navigator.language || navigator.userLanguage;
      const langCode = browserLang.split('-')[0];
      if (this.supportedLanguages[langCode]) {
        return this.supportedLanguages[langCode];
      }
      
      // 4. 默认返回英语
      return 'en';
    },
    
    // 显示语言推荐横幅
    showLanguageBanner: function(recommendedLang, currentLang) {
      if (recommendedLang === currentLang) return;
      
      const banner = document.createElement('div');
      banner.className = 'language-recommendation-banner';
      banner.innerHTML = `
        <div class="banner-content">
          <span class="banner-text">
            We detected you might prefer ${this.getLanguageName(recommendedLang)}. 
          </span>
          <button class="banner-switch-btn" data-lang="${recommendedLang}">
            Switch to ${this.getLanguageName(recommendedLang)}
          </button>
          <button class="banner-close-btn">&times;</button>
        </div>
      `;
      
      document.body.insertBefore(banner, document.body.firstChild);
      
      // 绑定事件
      banner.querySelector('.banner-switch-btn').addEventListener('click', () => {
        this.switchLanguage(recommendedLang);
      });
      
      banner.querySelector('.banner-close-btn').addEventListener('click', () => {
        banner.remove();
        localStorage.setItem('language_banner_dismissed', 'true');
      });
    },
    
    // 切换语言
    switchLanguage: function(langCode) {
      localStorage.setItem('preferred_language', langCode);
      
      // 使用Shopify的语言切换机制
      const form = document.querySelector('[data-disclosure-form]');
      if (form) {
        const input = form.querySelector('[data-disclosure-input]');
        if (input) {
          input.value = langCode;
          form.submit();
        }
      }
    },
    
    // 获取语言显示名称
    getLanguageName: function(langCode) {
      const names = {
        'en': 'English',
        'es': 'Español',
        'fr': 'Français', 
        'de': 'Deutsch',
        'it': 'Italiano',
        'pt-PT': 'Português',
        'zh-CN': '中文',
        'ja': '日本語',
        'ko': '한국어'
      };
      return names[langCode] || langCode;
    },
    
    // 初始化
    init: function() {
      const currentLang = document.documentElement.lang || 'en';
      const recommendedLang = this.detectUserLanguage();
      const bannerDismissed = localStorage.getItem('language_banner_dismissed');
      
      if (!bannerDismissed && recommendedLang !== currentLang) {
        // 延迟显示横幅，避免影响页面加载
        setTimeout(() => {
          this.showLanguageBanner(recommendedLang, currentLang);
        }, 2000);
      }
    }
  };
  
  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => LanguageDetector.init());
  } else {
    LanguageDetector.init();
  }
  
  // 暴露到全局作用域
  window.LanguageDetector = LanguageDetector;
})();
```

### 阶段四：用户体验优化

#### 4.1 语言推荐横幅样式
**文件：** `assets/language-banner.css`

```css
.language-recommendation-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 20px;
  z-index: 10000;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transform: translateY(-100%);
  animation: slideDown 0.5s ease forwards;
}

@keyframes slideDown {
  to {
    transform: translateY(0);
  }
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  max-width: 1200px;
  margin: 0 auto;
}

.banner-text {
  font-size: 14px;
  font-weight: 500;
}

.banner-switch-btn {
  background: rgba(255,255,255,0.2);
  border: 1px solid rgba(255,255,255,0.3);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.banner-switch-btn:hover {
  background: rgba(255,255,255,0.3);
  transform: translateY(-1px);
}

.banner-close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .banner-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  
  .banner-text {
    font-size: 13px;
  }
}
```

## 📝 实施步骤清单

### 第一周：基础设施搭建
- [ ] 修改 `header-tech-dark.liquid` 添加语言选择器
- [ ] 添加语言选择器CSS样式
- [ ] 测试Header语言选择器功能

### 第二周：语言文件扩展  
- [ ] 创建中文简体语言文件 (`zh-CN.json`)
- [ ] 创建日语语言文件 (`ja.json`) 
- [ ] 创建韩语语言文件 (`ko.json`)
- [ ] 在Shopify后台启用新语言

### 第三周：智能检测功能
- [ ] 创建 `language-detector.js` 脚本
- [ ] 创建 `language-banner.css` 样式文件
- [ ] 在主题中引入新的资源文件
- [ ] 测试语言自动检测功能

### 第四周：优化和测试
- [ ] 完善所有页面的翻译内容
- [ ] 测试各语言版本的显示效果
- [ ] 优化移动端体验
- [ ] 性能测试和优化

## 🔧 技术要求

### 必需的Shopify功能
- Shopify Markets (多市场功能)
- Shopify Translate & Adapt app (可选，用于管理翻译)

### 浏览器兼容性
- Chrome 60+
- Firefox 55+  
- Safari 12+
- Edge 79+

### 性能考虑
- 语言文件按需加载
- 缓存用户语言偏好
- 最小化JavaScript执行时间

## 📊 预期效果

### 用户体验提升
- **语言切换便利性**：Header显著位置的语言选择器
- **智能推荐**：基于浏览器语言自动推荐合适语言
- **无缝切换**：保持当前页面状态的语言切换

### 业务价值
- **市场覆盖**：支持主要国际市场语言
- **转化率提升**：本地化体验提高用户信任度
- **SEO优化**：多语言内容提升搜索引擎排名

### 技术优势
- **可维护性**：标准化的Shopify多语言架构
- **可扩展性**：易于添加新语言支持
- **性能优化**：智能加载和缓存机制

## 🚨 注意事项

1. **翻译质量**：建议使用专业翻译服务，避免机器翻译
2. **文化适应**：考虑不同文化的设计和内容偏好
3. **法律合规**：确保符合各国的电商法律要求
4. **测试覆盖**：在不同语言环境下全面测试功能
5. **SEO配置**：正确设置hreflang标签和URL结构

## 💻 代码实施示例

### 完整的Header语言选择器实现

**步骤1：修改 `sections/header-tech-dark.liquid`**

在第1150行左右（购物车图标之前）添加：

```liquid
<!-- 语言选择器 - 添加到购物车图标之前 -->
{% if shop.enabled_locales.size > 1 %}
<div class="tech-nav-item tech-language-selector">
  {%- form 'localization', class: 'tech-localization-form', data-disclosure-form: '' -%}
    <div class="tech-disclosure" id="HeaderLanguage">
      <button type="button" class="tech-language-toggle" aria-expanded="false"
              aria-controls="LanguageList" data-disclosure-toggle>
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" class="tech-language-icon">
          <path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/>
        </svg>
        <span class="tech-language-text">{{ form.current_locale.endonym_name | capitalize }}</span>
        <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor" class="tech-chevron">
          <path d="M3 5L6 8L9 5" stroke="currentColor" stroke-width="1.5" fill="none"/>
        </svg>
      </button>
      <ul id="LanguageList" class="tech-language-list" data-disclosure-list>
        {%- for locale in form.available_locales -%}
          <li class="tech-language-item {% if locale.iso_code == form.current_locale.iso_code %}tech-language-item--current{% endif %}">
            <a class="tech-language-option" href="#" lang="{{ locale.iso_code }}"
               {% if locale.iso_code == form.current_locale.iso_code %}aria-current="true"{% endif %}
               data-value="{{ locale.iso_code }}" data-disclosure-option>
              <span class="tech-language-flag">{{ locale.iso_code | upcase }}</span>
              <span class="tech-language-name">{{ locale.endonym_name | capitalize }}</span>
              {% if locale.iso_code == form.current_locale.iso_code %}
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" class="tech-check-icon">
                  <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                </svg>
              {% endif %}
            </a>
          </li>
        {%- endfor -%}
      </ul>
      <input type="hidden" name="locale_code" value="{{ form.current_locale.iso_code }}" data-disclosure-input/>
    </div>
  {%- endform -%}
</div>
{% endif %}
```

**步骤2：添加JavaScript初始化**

在 `sections/header-tech-dark.liquid` 的 `<script>` 部分添加：

```javascript
// 语言选择器初始化
document.addEventListener('DOMContentLoaded', function() {
  // 初始化语言选择器的Disclosure功能
  const languageSelector = document.querySelector('.tech-language-selector [data-disclosure-form]');
  if (languageSelector && typeof theme !== 'undefined' && theme.Disclosure) {
    new theme.Disclosure(languageSelector);
  }

  // 添加语言切换动画
  const languageOptions = document.querySelectorAll('.tech-language-option');
  languageOptions.forEach(option => {
    option.addEventListener('click', function(e) {
      if (!this.hasAttribute('aria-current')) {
        // 添加加载状态
        const toggle = document.querySelector('.tech-language-toggle');
        toggle.classList.add('loading');
        toggle.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" class="tech-loading-icon">
            <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z" />
          </svg>
          <span>Switching...</span>
        `;
      }
    });
  });
});
```

### 语言文件模板

**中文简体完整模板 (`locales/zh-CN.json`)：**

```json
{
  "general": {
    "404": {
      "title": "404 页面未找到",
      "subtext_html": "<p>您要查找的页面不存在。</p><p><a href='{{ url }}'>继续购物</a></p>"
    },
    "accessibility": {
      "skip_to_content": "跳转到内容",
      "close_modal": "关闭 (esc)",
      "close": "关闭",
      "learn_more": "了解更多"
    },
    "language": {
      "dropdown_label": "语言"
    },
    "currency": {
      "dropdown_label": "货币"
    },
    "pagination": {
      "previous": "上一页",
      "next": "下一页"
    }
  },
  "layout": {
    "cart": {
      "title": "购物车"
    },
    "customer": {
      "account": "账户",
      "log_out": "退出登录",
      "log_in": "登录",
      "create_account": "创建账户"
    },
    "footer": {
      "social_platform": "{{ platform }} 上的 {{ name }}"
    }
  },
  "products": {
    "product": {
      "sold_out": "售罄",
      "unavailable": "不可用",
      "add_to_cart": "加入购物车",
      "price": "价格",
      "in_stock_label": "有库存",
      "stock_label": {
        "one": "仅剩 [count] 件",
        "other": "仅剩 [count] 件"
      }
    }
  },
  "cart": {
    "general": {
      "title": "购物车",
      "empty": "您的购物车是空的",
      "continue_shopping": "继续购物",
      "update": "更新购物车",
      "checkout": "结账",
      "subtotal": "小计",
      "savings_html": "您节省了 [savings]"
    }
  }
}
```

### Shopify后台配置步骤

**1. 启用多语言功能：**
- 进入 Shopify Admin → Settings → Languages
- 点击 "Add language"
- 选择要添加的语言（如中文简体）
- 上传对应的 JSON 语言文件

**2. 配置Markets（可选）：**
- 进入 Settings → Markets
- 为不同语言创建对应的市场
- 设置货币和配送选项

**3. URL结构配置：**
- 语言URL前缀：`/zh-cn/`, `/ja/`, `/ko/` 等
- 确保SEO设置中启用了hreflang标签

### 测试清单

**功能测试：**
- [ ] Header语言选择器显示正确
- [ ] 点击语言选项能正确切换
- [ ] 当前语言有正确的选中状态
- [ ] 移动端语言选择器工作正常
- [ ] Footer语言选择器保持正常工作

**内容测试：**
- [ ] 所有界面文本正确翻译
- [ ] 产品信息支持多语言
- [ ] 购物车和结账流程多语言支持
- [ ] 错误信息正确本地化

**SEO测试：**
- [ ] 每个语言版本有正确的hreflang标签
- [ ] URL结构符合SEO最佳实践
- [ ] 语言切换不影响页面SEO权重

**性能测试：**
- [ ] 语言切换响应时间 < 2秒
- [ ] 语言文件加载不影响首屏渲染
- [ ] 移动端性能表现良好

## 🔄 维护和更新

### 定期维护任务

**每月：**
- 检查新增内容的翻译完整性
- 更新产品描述的多语言版本
- 监控各语言版本的转化率数据

**每季度：**
- 评估新语言市场的需求
- 优化翻译质量和用户体验
- 更新语言检测算法

**每年：**
- 全面审查所有语言文件
- 评估语言支持的ROI
- 考虑添加新的目标市场语言

### 扩展建议

**短期扩展（3个月内）：**
- 添加阿拉伯语支持（RTL布局）
- 实现基于地理位置的语言推荐
- 添加语言切换的用户行为分析

**中期扩展（6个月内）：**
- 集成专业翻译管理系统
- 实现动态内容的实时翻译
- 添加语音语言切换功能

**长期扩展（1年内）：**
- AI驱动的个性化语言推荐
- 多语言客服聊天机器人
- 基于用户行为的语言优化

## 🎯 IR3 V2 产品页面国际化方案

### 页面结构分析

**当前 `http://127.0.0.1:9292/pages/ir3-v2-show` 页面包含以下组件：**

1. **Hero Section** (`IR3-Hero-Section-1`)
2. **Key Features** (`ir3-v2-key-features`)
3. **Auto Leveling Animation** (`ir3-v2-auto-leveling-frames`)
4. **Tech Innovation** (`ir3-tech-innovation`)
5. **Video Scroll Demo** (`ir3-video-scroll-demo`)
6. **Performance Video** (`ir3-performance-video-scroll`)
7. **Smart Features** (`smart_features`)
8. **Filament Sensor** (`ir3-filament-sensor`)
9. **Batch Printing Video** (`ir3-batch-printing-video`)
10. **Long Object Printing** (`ir3-long-object-printing`)
11. **Parameter Display** (`ir3-parameter-display`)

### 国际化实施策略

#### 阶段一：核心文案国际化

**1. Hero Section 多语言配置**

修改 `templates/page.json` 中的Hero Section设置，使用翻译键：

```json
{
  "main_title": "{{ 'ir3_v2.hero.main_title' | t }}",
  "sub_title": "{{ 'ir3_v2.hero.sub_title' | t }}",
  "tagline": "{{ 'ir3_v2.hero.tagline' | t }}",
  "primary_button_text": "{{ 'ir3_v2.hero.shop_now' | t }}",
  "secondary_button_text": "{{ 'ir3_v2.hero.view_specs' | t }}",
  "scroll_text": "{{ 'ir3_v2.hero.scroll_text' | t }}",
  "badge_1_text": "{{ 'ir3_v2.hero.badge_star' | t }}",
  "badge_2_text": "{{ 'ir3_v2.hero.badge_fast' | t }}",
  "badge_3_text": "{{ 'ir3_v2.hero.badge_infinite' | t }}"
}
```

**2. Key Features 多语言配置**

```json
{
  "main_title": "{{ 'ir3_v2.features.main_title' | t }}",
  "subtitle": "{{ 'ir3_v2.features.subtitle' | t }}",
  "feature_1_title": "{{ 'ir3_v2.features.batch.title' | t }}",
  "feature_1_description": "{{ 'ir3_v2.features.batch.description' | t }}",
  "feature_1_bullet_1": "{{ 'ir3_v2.features.batch.bullet_1' | t }}",
  "feature_1_bullet_2": "{{ 'ir3_v2.features.batch.bullet_2' | t }}",
  "feature_1_bullet_3": "{{ 'ir3_v2.features.batch.bullet_3' | t }}"
}
```

#### 阶段二：语言文件扩展

**中文简体 IR3 V2 专用翻译 (`locales/zh-CN.json`)：**

```json
{
  "ir3_v2": {
    "hero": {
      "main_title": "IdeaFormer IR3 V2",
      "sub_title": "专业传送带3D打印机",
      "tagline": "突破Z轴限制，开启连续打印新时代。",
      "shop_now": "立即购买",
      "view_specs": "查看规格",
      "scroll_text": "滚动探索",
      "badge_star": "明星产品",
      "badge_fast": "高速打印",
      "badge_infinite": "无限Z轴"
    },
    "features": {
      "main_title": "核心特性",
      "subtitle": "在一台机器中体验三项突破性创新：无限批量生产、无限Z轴打印和免支撑悬垂技术。",
      "batch": {
        "title": "无限批量生产",
        "description": "重复打印相同模型或一次打印多个模型。先测试和调整切片设置，然后开始批量生产。",
        "bullet_1": "一批次多个模型",
        "bullet_2": "完美适合批量生产",
        "bullet_3": "打印质量一致"
      },
      "unlimited": {
        "title": "突破Z轴限制",
        "description": "打印无长度限制的模型。完美适合建筑模型、长工具和超大原型。",
        "bullet_1": "无限长度打印",
        "bullet_2": "完美适合建筑模型",
        "bullet_3": "无Z轴高度限制"
      },
      "support_free": {
        "title": "免支撑悬垂打印",
        "description": "背面打印角度无需支撑结构，节省材料和后处理时间。",
        "bullet_1": "悬垂无需支撑",
        "bullet_2": "节省材料和后处理时间",
        "bullet_3": "表面质量更佳"
      }
    },
    "auto_leveling": {
      "main_title": "IR3 V2 智能调平系统",
      "main_description": "6点精准检测，0.01mm超高精度，确保每次打印都完美无瑕。",
      "hotspot_1_title": "初始位置检测",
      "hotspot_1_description": "打印头移动至传送带前端，准备开始智能调平程序",
      "hotspot_2_title": "精准探测接触",
      "hotspot_2_description": "打印头缓慢下降，探测传送带表面的精确高度位置",
      "hotspot_3_title": "Y轴偏移计算",
      "hotspot_3_description": "系统记录并计算Y轴的偏移量，准备进行补偿调整",
      "hotspot_4_title": "多点精度验证",
      "hotspot_4_description": "沿传送带进行多次接触测试，确保调平精度达到0.01mm",
      "hotspot_5_title": "智能补偿应用",
      "hotspot_5_description": "系统自动应用Y轴补偿值，实现完美的打印平面",
      "hotspot_6_title": "调平完成确认",
      "hotspot_6_description": "6点智能调平完成，系统已准备进行高精度45°角打印"
    },
    "tech_innovation": {
      "title": "技术创新",
      "subtitle": "突破性技术，重新定义3D打印标准"
    },
    "smart_features": {
      "title": "智能功能",
      "subtitle": "先进的智能化功能，让3D打印更简单"
    },
    "filament_sensor": {
      "title": "智能断料检测",
      "description": "智能断料检测系统，当断料流动停止超过12mm时暂停打印。自动检测断料、堵塞或进料问题，防止打印失败。"
    },
    "batch_printing": {
      "title": "批量打印",
      "subtitle": "高效批量生产解决方案"
    },
    "long_object": {
      "title": "长物体打印",
      "subtitle": "突破传统尺寸限制"
    },
    "parameters": {
      "title": "技术参数",
      "subtitle": "IdeaFormer IR3 V2 3D打印机的全面技术规格和功能概览"
    }
  }
}
```

**日语翻译 (`locales/ja.json`)：**

```json
{
  "ir3_v2": {
    "hero": {
      "main_title": "IdeaFormer IR3 V2",
      "sub_title": "プロフェッショナルコンベアベルト3Dプリンター",
      "tagline": "Z軸の制限を突破し、連続印刷の新時代を切り開く。",
      "shop_now": "今すぐ購入",
      "view_specs": "仕様を見る",
      "scroll_text": "スクロールして探索",
      "badge_star": "スター製品",
      "badge_fast": "高速印刷",
      "badge_infinite": "無限Z軸"
    },
    "features": {
      "main_title": "主要機能",
      "subtitle": "一台のマシンで3つの画期的なイノベーションを体験：無限バッチ生産、無限Z軸印刷、サポートフリーオーバーハング技術。",
      "batch": {
        "title": "無限バッチ生産",
        "description": "同じモデルを繰り返し印刷するか、複数のモデルを一度に印刷します。まずスライス設定をテストして調整し、その後大量生産を開始します。",
        "bullet_1": "1バッチで複数モデル",
        "bullet_2": "大量生産に最適",
        "bullet_3": "印刷品質の一貫性"
      }
    },
    "auto_leveling": {
      "main_title": "IR3 V2 スマートレベリングシステム",
      "main_description": "6点精密検出、0.01mm超高精度で、すべての印刷を完璧に。",
      "hotspot_1_title": "初期位置検出",
      "hotspot_1_description": "プリントヘッドがコンベアベルトの前端に移動し、スマートレベリングプログラムの開始準備をします"
    }
  }
}
```

#### 阶段三：组件模板国际化

**1. 修改 Hero Section 组件**

**文件：** `sections/IR3-Hero-Section-1.liquid`

将硬编码文本替换为翻译键：

```liquid
<!-- 原始代码 -->
<h1 class="hero-title">{{ section.settings.main_title }}</h1>

<!-- 国际化后 -->
<h1 class="hero-title">
  {% if section.settings.main_title != blank %}
    {{ section.settings.main_title }}
  {% else %}
    {{ 'ir3_v2.hero.main_title' | t }}
  {% endif %}
</h1>
```

**2. 修改 Key Features 组件**

**文件：** `sections/ir3-v2-key-features.liquid`

```liquid
<!-- 标题国际化 -->
<h2 class="features-title">
  {% if section.settings.main_title != blank %}
    {{ section.settings.main_title }}
  {% else %}
    {{ 'ir3_v2.features.main_title' | t }}
  {% endif %}
</h2>

<!-- 副标题国际化 -->
<p class="features-subtitle">
  {% if section.settings.subtitle != blank %}
    {{ section.settings.subtitle }}
  {% else %}
    {{ 'ir3_v2.features.subtitle' | t }}
  {% endif %}
</p>
```

**3. 修改 Auto Leveling 组件**

**文件：** `sections/ir3-v2-auto-leveling-frames.liquid`

```liquid
<!-- 热点标题国际化 -->
{% for i in (1..6) %}
  {% assign hotspot_title_key = 'hotspot_' | append: i | append: '_title' %}
  {% assign hotspot_desc_key = 'hotspot_' | append: i | append: '_description' %}

  <div class="hotspot-text">
    <h3>
      {% if section.settings[hotspot_title_key] != blank %}
        {{ section.settings[hotspot_title_key] }}
      {% else %}
        {{ 'ir3_v2.auto_leveling.hotspot_' | append: i | append: '_title' | t }}
      {% endif %}
    </h3>
    <p>
      {% if section.settings[hotspot_desc_key] != blank %}
        {{ section.settings[hotspot_desc_key] }}
      {% else %}
        {{ 'ir3_v2.auto_leveling.hotspot_' | append: i | append: '_description' | t }}
      {% endif %}
    </p>
  </div>
{% endfor %}
```

#### 阶段四：Schema 配置国际化

**修改组件的 Schema 配置，支持多语言标签：**

```json
{
  "name": {
    "en": "IR3 Hero Section",
    "zh-CN": "IR3 英雄区块",
    "ja": "IR3 ヒーローセクション"
  },
  "settings": [
    {
      "type": "text",
      "id": "main_title",
      "label": {
        "en": "Main Title",
        "zh-CN": "主标题",
        "ja": "メインタイトル"
      },
      "default": "IdeaFormer IR3 V2",
      "info": {
        "en": "Leave blank to use automatic translation",
        "zh-CN": "留空使用自动翻译",
        "ja": "空白にすると自動翻訳を使用"
      }
    }
  ]
}
```

### IR3 V2 页面国际化实施清单

#### 第一阶段：基础文案翻译（1周）
- [ ] 创建 IR3 V2 专用翻译键结构
- [ ] 翻译 Hero Section 所有文案
- [ ] 翻译 Key Features 所有文案
- [ ] 翻译 Auto Leveling 热点文案

#### 第二阶段：组件模板修改（1周）
- [ ] 修改 `IR3-Hero-Section-1.liquid` 支持翻译键
- [ ] 修改 `ir3-v2-key-features.liquid` 支持翻译键
- [ ] 修改 `ir3-v2-auto-leveling-frames.liquid` 支持翻译键
- [ ] 修改其他相关组件模板

#### 第三阶段：高级功能翻译（1周）
- [ ] 翻译技术参数和规格信息
- [ ] 翻译视频组件的文案
- [ ] 翻译智能功能描述
- [ ] 翻译批量打印和长物体打印文案

#### 第四阶段：测试和优化（1周）
- [ ] 测试所有语言版本的显示效果
- [ ] 优化长文本在不同语言下的排版
- [ ] 测试移动端多语言显示
- [ ] 性能测试和SEO优化

### 特殊考虑事项

#### 1. 技术术语处理
- **3D打印专业术语**：保持英文原文或提供标准翻译
- **产品型号**：IR3 V2 保持不变
- **技术参数**：数值保持不变，单位可本地化

#### 2. 视觉适配
- **中文字体**：确保中文字体正确显示
- **文本长度**：不同语言文本长度差异的布局适配
- **RTL语言**：为阿拉伯语等RTL语言预留适配空间

#### 3. SEO优化
- **URL结构**：`/zh-cn/pages/ir3-v2-show`
- **Meta标签**：页面标题和描述的多语言版本
- **结构化数据**：产品信息的多语言标记

---

*本实施方案提供了完整的代码示例和配置步骤，可以直接应用到您的Shopify项目中。建议按阶段实施，确保每个功能都经过充分测试后再进行下一步。*
