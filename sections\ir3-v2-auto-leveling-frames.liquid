{%- style -%}
  .ir3-leveling-frames {
    position: relative;
    width: 100%;
    height: 100vh;
    min-height: 100vh; /* 修复：确保最小高度，防止白色空白 */
    background: {{ section.settings.background_color | default: '#0a0a0a' }};
    overflow: hidden;
    /* 修复滚动显示异常：移除可能导致层级冲突的设置 */
    z-index: 1;
    /* 修复：确保组件在文档流中的正确位置 */
    margin: 0;
    padding: 0;
  }

  .ir3-leveling-frames__container {
    position: relative; /* 修复：从sticky改为relative，避免"拉窗帘"效果 */
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    /* 确保正常的文档流 */
    z-index: 2;
  }

  /* 背景图片容器 */
  .ir3-leveling-frames__background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    opacity: 1;
    transition: opacity 0.8s ease-out;
  }

  .ir3-leveling-frames__background.hidden {
    opacity: 0;
  }

  .ir3-leveling-frames__background-image {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: blur(10px);
    will-change: filter, opacity;
  }

  /* Canvas容器 */
  .ir3-leveling-frames__canvas-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.8s ease-in;
  }

  .ir3-leveling-frames__canvas-container.loaded {
    opacity: 1;
  }

  .ir3-leveling-frames__canvas {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    object-fit: cover;
    will-change: transform;
    backface-visibility: hidden;
  }

  /* 加载指示器 - 移除背景 */
  .ir3-leveling-frames__loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 10;
    opacity: 1;
    transition: opacity 0.5s ease;
    /* 移除背景相关样式 */
  }

  .ir3-leveling-frames__loading.hidden {
    opacity: 0;
    pointer-events: none;
  }

  .ir3-leveling-frames__loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
  }

  .ir3-leveling-frames__loading-text {
    font-size: 18px;
    margin-bottom: 20px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 400;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    /* 增强文字可读性 */
  }

  .ir3-leveling-frames__loading-progress {
    width: 300px;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin: 0 auto;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    /* 增加阴影增强可见性 */
  }

  .ir3-leveling-frames__loading-bar {
    height: 100%;
    background: linear-gradient(90deg, #ffffff, #f0f0f0);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 3px;
  }

  /* 主标题 */
  .ir3-leveling-frames__title {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 5;
    opacity: 0;
    transition: opacity 0.8s ease;
  }

  .ir3-leveling-frames__title.visible {
    opacity: 1;
  }

  .ir3-leveling-frames__main-title {
    font-size: {{ section.settings.title_size | default: 48 }}px;
    font-weight: 700;
    font-family: 'Montserrat', sans-serif;
    color: #ffffff;
    margin-bottom: 20px;
    line-height: 1.2;
    text-shadow: 0 4px 20px rgba(255,255,255,0.3);
  }

  .ir3-leveling-frames__description {
    font-size: {{ section.settings.description_size | default: 20 }}px;
    font-family: 'Montserrat', sans-serif;
    color: #ffffffd9;
    line-height: 1.6;
    max-width: 800px;
  }

  /* 热点系统 */
  .ir3-leveling-frames__hotspots {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    z-index: 3;
    pointer-events: none;
  }

  /* 🔍 调试模式样式 */
  .ir3-leveling-frames__debug-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 20;
    {% unless section.settings.show_debug_mode %}display: none;{% endunless %}
  }

  .ir3-leveling-frames__debug-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      repeating-linear-gradient(0deg, rgba(255,255,255,0.2) 0, rgba(255,255,255,0.2) 1px, transparent 1px, transparent 10%),
      repeating-linear-gradient(90deg, rgba(255,255,255,0.2) 0, rgba(255,255,255,0.2) 1px, transparent 1px, transparent 10%);
  }

  .ir3-leveling-frames__debug-canvas-outline {
    position: absolute;
    border: 2px solid #ff0000;
    background: rgba(255,0,0,0.1);
    pointer-events: none;
  }

  .ir3-leveling-frames__debug-info {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0,0,0,0.9);
    color: #fff;
    padding: 15px;
    border-radius: 8px;
    font-family: monospace;
    font-size: 12px;
    z-index: 100;
    min-width: 280px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.5);
    {% unless section.settings.show_debug_mode %}display: none;{% endunless %}
  }

  .ir3-leveling-frames__debug-info h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #ffd700;
    border-bottom: 1px solid rgba(255,255,255,0.3);
    padding-bottom: 5px;
  }

  .ir3-leveling-frames__debug-item {
    margin: 5px 0;
    display: flex;
    justify-content: space-between;
  }

  .ir3-leveling-frames__debug-item label {
    color: #888;
  }

  .ir3-leveling-frames__debug-item span {
    color: #0ff;
    font-weight: bold;
  }

  .ir3-leveling-frames__mouse-coords {
    position: absolute;
    background: rgba(255,0,0,0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-family: monospace;
    pointer-events: none;
    z-index: 22;
    transform: translate(10px, -30px);
    {% unless section.settings.show_debug_mode %}display: none;{% endunless %}
  }

  .ir3-leveling-frames__hotspot {
    position: absolute;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid #ffffff;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
    cursor: pointer;
    transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
    pointer-events: auto;
  }

  .ir3-leveling-frames__hotspot.active {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }

  .ir3-leveling-frames__hotspot::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background: #ffffff;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 2s infinite;
  }

  .ir3-leveling-frames__hotspot::after {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    opacity: 0;
    animation: ripple 2s infinite;
  }

  /* 文本面板 */
  .ir3-leveling-frames__text-panel {
    position: fixed;
    width: 280px;
    max-width: 80vw;
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(249,250,251,0.9) 100%);
    padding: 16px;
    border-radius: 10px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.3);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    opacity: 0;
    z-index: 10;
    pointer-events: none;
    transition: opacity 0.3s ease-in-out;
  }

  .ir3-leveling-frames__text-panel.active {
    opacity: 1;
  }

  .ir3-leveling-frames__text-title {
    font-size: 20px;
    font-weight: 700;
    font-family: 'Montserrat', sans-serif;
    color: #1a1a1a;
    margin-bottom: 12px;
    line-height: 1.3;
  }

  .ir3-leveling-frames__text-description {
    font-size: 14px;
    color: #4a5568;
    line-height: 1.6;
    margin: 0;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 0.7; transform: translate(-50%, -50%) scale(1.2); }
  }

  @keyframes ripple {
    0% { transform: scale(0.8); opacity: 1; }
    100% { transform: scale(2); opacity: 0; }
  }

  /* 移动端优化 */
  @media (max-width: 768px) {
    .ir3-leveling-frames {
      height: 100vh; /* 移动端也使用100vh */
    }

    .ir3-leveling-frames__title {
      width: 90%; /* 增加标题容器宽度，充分利用屏幕空间 */
      max-width: none; /* 移除最大宽度限制 */
      padding: 0 20px; /* 添加左右内边距 */
    }

    .ir3-leveling-frames__main-title {
      font-size: {{ section.settings.title_size | default: 48 | times: 0.75 }}px;
      line-height: 1.1; /* 稍微紧凑的行高 */
      word-break: break-word; /* 允许长单词换行 */
      hyphens: auto; /* 自动连字符 */
    }

    .ir3-leveling-frames__description {
      font-size: {{ section.settings.description_size | default: 20 | times: 0.85 }}px;
      line-height: 1.3;
    }

    .ir3-leveling-frames__hotspot {
      width: 24px;
      height: 24px;
    }

    .ir3-leveling-frames__text-panel {
      width: 240px;
      padding: 12px;
    }

    .ir3-leveling-frames__text-title {
      font-size: 16px;
    }

    .ir3-leveling-frames__text-description {
      font-size: 12px;
    }

    .ir3-leveling-frames__loading-progress {
      width: 250px;
    }

    .ir3-leveling-frames__loading-text {
      font-size: 16px;
    }
  }
{%- endstyle -%}

<div class="ir3-leveling-frames" id="ir3-leveling-frames-{{ section.id }}">
  <div class="ir3-leveling-frames__container">
    
    <!-- 背景图片容器 -->
    <div class="ir3-leveling-frames__background" id="ir3-background-{{ section.id }}">
      <img 
        class="ir3-leveling-frames__background-image" 
        id="ir3-background-image-{{ section.id }}"
        style="display: none;"
        alt="Loading background"
      />
    </div>
    
    <!-- Canvas容器 -->
    <div class="ir3-leveling-frames__canvas-container" id="ir3-canvas-container-{{ section.id }}">
      <canvas 
        class="ir3-leveling-frames__canvas"
        id="ir3-canvas-{{ section.id }}"
        data-frame-count="{{ section.settings.frame_count | default: 244 }}"
        data-frame-base-url="{{ section.settings.frame_base_url | default: 'https://cdn.shopify.com/s/files/1/0762/6113/0493/files/' }}"
        data-frame-prefix="{{ section.settings.frame_prefix | default: 'frame_' }}"
        data-frame-suffix="{{ section.settings.frame_suffix | default: '.webp' }}"
      ></canvas>
    </div>

    <!-- 加载指示器 -->
    <div class="ir3-leveling-frames__loading" id="ir3-loading-{{ section.id }}">
      <div class="ir3-leveling-frames__loading-spinner"></div>
      <div class="ir3-leveling-frames__loading-text">Loading frames...</div>
      <div class="ir3-leveling-frames__loading-progress">
        <div class="ir3-leveling-frames__loading-bar" id="ir3-loading-bar-{{ section.id }}"></div>
      </div>
    </div>

    <!-- 主标题 -->
    {% if section.settings.show_title %}
    <div class="ir3-leveling-frames__title" id="ir3-title-{{ section.id }}">
      <h2 class="ir3-leveling-frames__main-title">{{ section.settings.main_title | default: "IR3 V2 Smart Leveling System" }}</h2>
      <p class="ir3-leveling-frames__description">{{ section.settings.main_description | default: "6-point precise detection, 0.01mm ultra-high precision, ensuring every print is flawless." }}</p>
    </div>
    {% endif %}

    <!-- 热点系统 -->
    <div class="ir3-leveling-frames__hotspots" id="ir3-hotspots-{{ section.id }}">
      {% for i in (1..6) %}
        {% assign hotspot_x = 'hotspot_' | append: i | append: '_x' %}
        {% assign hotspot_y = 'hotspot_' | append: i | append: '_y' %}
        {% assign hotspot_start = 'hotspot_' | append: i | append: '_start_time' %}
        {% assign hotspot_end = 'hotspot_' | append: i | append: '_end_time' %}
        {% assign hotspot_title = 'hotspot_' | append: i | append: '_title' %}
        {% assign hotspot_description = 'hotspot_' | append: i | append: '_description' %}

        <div
          class="ir3-leveling-frames__hotspot"
          id="ir3-hotspot-{{ section.id }}-{{ i }}"
          style="left: {{ section.settings[hotspot_x] | default: 50 }}%; top: {{ section.settings[hotspot_y] | default: 50 }}%;"
          data-step="{{ i }}"
          data-start-time="{{ section.settings[hotspot_start] | default: 0 }}"
          data-end-time="{{ section.settings[hotspot_end] | default: 100 }}"
          data-title="{{ section.settings[hotspot_title] }}"
          data-description="{{ section.settings[hotspot_description] }}"
        >
          {% if section.settings.show_debug_mode %}
            <div style="position: absolute; top: -25px; left: 50%; transform: translateX(-50%); background: rgba(0,0,0,0.8); color: #0ff; padding: 2px 6px; border-radius: 3px; font-size: 10px; font-family: monospace; white-space: nowrap; pointer-events: none;">
              #{{ i }} [{{ section.settings[hotspot_start] | default: 0 }}-{{ section.settings[hotspot_end] | default: 100 }}%]
            </div>
          {% endif %}
        </div>
      {% endfor %}
    </div>

    <!-- 🔍 调试模式覆盖层 -->
    {% if section.settings.show_debug_mode %}
      <div class="ir3-leveling-frames__debug-overlay">
        <!-- 网格 -->
        <div class="ir3-leveling-frames__debug-grid"></div>
        <!-- Canvas区域轮廓 -->
        <div class="ir3-leveling-frames__debug-canvas-outline" id="ir3-debug-canvas-outline-{{ section.id }}"></div>
        <!-- 鼠标坐标 -->
        <div class="ir3-leveling-frames__mouse-coords" id="ir3-mouse-coords-{{ section.id }}">X: 0%, Y: 0%</div>
      </div>
    {% endif %}

    <!-- 文本面板 -->
    <div class="ir3-leveling-frames__text-panel" id="ir3-text-panel-{{ section.id }}">
      <h3 class="ir3-leveling-frames__text-title" id="ir3-text-title-{{ section.id }}"></h3>
      <p class="ir3-leveling-frames__text-description" id="ir3-text-description-{{ section.id }}"></p>
    </div>

  </div>
</div>

<!-- 🔍 调试信息面板 -->
{% if section.settings.show_debug_mode %}
  <div class="ir3-leveling-frames__debug-info" id="ir3-debug-info-{{ section.id }}">
    <h4>🔧 帧动画调试信息</h4>
    <div class="ir3-leveling-frames__debug-item">
      <label>鼠标位置:</label>
      <span id="ir3-debug-mouse-{{ section.id }}">X: 0%, Y: 0%</span>
    </div>
    <div class="ir3-leveling-frames__debug-item">
      <label>当前帧:</label>
      <span id="ir3-debug-frame-{{ section.id }}">0 / 244</span>
    </div>
    <div class="ir3-leveling-frames__debug-item">
      <label>播放阶段:</label>
      <span id="ir3-debug-stage-{{ section.id }}">未开始</span>
    </div>
    <div class="ir3-leveling-frames__debug-item">
      <label>Canvas区域:</label>
      <span id="ir3-debug-canvas-{{ section.id }}">0x0</span>
    </div>
    <div class="ir3-leveling-frames__debug-item">
      <label>活动热点:</label>
      <span id="ir3-debug-hotspot-{{ section.id }}">无</span>
    </div>
    <div class="ir3-leveling-frames__debug-item">
      <label>滚动进度:</label>
      <span id="ir3-debug-progress-{{ section.id }}">0%</span>
    </div>
  </div>
{% endif %}

<script>
// 延迟初始化，等待前面的组件加载完成
document.addEventListener('DOMContentLoaded', function() {
  // 检测前两个组件是否已加载完成
  const checkPreviousComponentsLoaded = () => {
    // 检查第一个组件（Hero Section）
    const heroSection = document.querySelector('[id*="ir3_hero_section"]');
    const heroLoaded = heroSection && heroSection.querySelector('img') &&
                      heroSection.querySelector('img').complete;

    // 检查第二个组件（Key Features）
    const featuresSection = document.querySelector('[id*="ir3_v2_key_features"]');
    const featuresLoaded = featuresSection &&
                          (featuresSection.querySelector('img') ?
                           featuresSection.querySelector('img').complete : true);

    console.log('🔍 检查前置组件加载状态:', {
      heroLoaded: heroLoaded,
      featuresLoaded: featuresLoaded
    });

    return heroLoaded && featuresLoaded;
  };

  // 如果前置组件已加载，立即初始化
  if (checkPreviousComponentsLoaded()) {
    console.log('✅ 前置组件已加载，立即初始化帧动画');
    initIR3LevelingFrames('{{ section.id }}');
  } else {
    console.log('⏳ 等待前置组件加载完成...');
    // 否则延迟初始化，给前置组件更多加载时间
    const initDelay = setTimeout(() => {
      console.log('⏰ 延迟时间到，开始初始化帧动画');
      initIR3LevelingFrames('{{ section.id }}');
    }, 2000); // 2秒延迟

    // 同时监听图片加载完成事件
    const images = document.querySelectorAll('img');
    let loadedCount = 0;
    const totalImages = Math.min(images.length, 10); // 最多检查前10张图片

    images.forEach((img, index) => {
      if (index < 10) { // 只检查前10张图片
        if (img.complete) {
          loadedCount++;
        } else {
          img.addEventListener('load', () => {
            loadedCount++;
            if (loadedCount >= totalImages * 0.8) { // 80%的图片加载完成
              console.log('🖼️ 大部分图片已加载，提前初始化帧动画');
              clearTimeout(initDelay);
              initIR3LevelingFrames('{{ section.id }}');
            }
          });
        }
      }
    });
  }
});

function initIR3LevelingFrames(sectionId) {
  console.log('🚀 初始化IR3调平帧动画组件:', sectionId);

  // 全局变量
  let currentActiveHotspot = -1;
  const showDebugMode = {{ section.settings.show_debug_mode | default: false }};
  let canvasRenderInfo = { // 存储Canvas实际渲染区域信息
    width: 0,
    height: 0,
    offsetX: 0,
    offsetY: 0
  };

  // 🔍 调试模式元素
  const debugElements = showDebugMode ? {
    mouseCoords: document.getElementById('ir3-mouse-coords-' + sectionId),
    debugMouse: document.getElementById('ir3-debug-mouse-' + sectionId),
    debugFrame: document.getElementById('ir3-debug-frame-' + sectionId),
    debugStage: document.getElementById('ir3-debug-stage-' + sectionId),
    debugCanvas: document.getElementById('ir3-debug-canvas-' + sectionId),
    debugHotspot: document.getElementById('ir3-debug-hotspot-' + sectionId),
    debugProgress: document.getElementById('ir3-debug-progress-' + sectionId),
    canvasOutline: document.getElementById('ir3-debug-canvas-outline-' + sectionId)
  } : {};

  // 检查GSAP和ScrollTrigger
  if (typeof gsap === 'undefined' || typeof ScrollTrigger === 'undefined') {
    console.error('❌ GSAP或ScrollTrigger未加载');
    return;
  }

  gsap.registerPlugin(ScrollTrigger);

  // 获取DOM元素
  const section = document.getElementById('ir3-leveling-frames-' + sectionId);
  const canvas = document.getElementById('ir3-canvas-' + sectionId);
  const canvasContainer = document.getElementById('ir3-canvas-container-' + sectionId);
  const backgroundContainer = document.getElementById('ir3-background-' + sectionId);
  const backgroundImage = document.getElementById('ir3-background-image-' + sectionId);
  const loadingIndicator = document.getElementById('ir3-loading-' + sectionId);
  const loadingBar = document.getElementById('ir3-loading-bar-' + sectionId);
  const titleElement = document.getElementById('ir3-title-' + sectionId);
  const textPanel = document.getElementById('ir3-text-panel-' + sectionId);
  const textTitle = document.getElementById('ir3-text-title-' + sectionId);
  const textDescription = document.getElementById('ir3-text-description-' + sectionId);
  const hotspots = document.querySelectorAll('#ir3-hotspots-' + sectionId + ' .ir3-leveling-frames__hotspot');

  // 🔍 初始化调试模式
  if (showDebugMode) {
    initDebugMode();
  }

  console.log('📋 找到的元素:', {
    section: !!section,
    canvas: !!canvas,
    canvasContainer: !!canvasContainer,
    backgroundContainer: !!backgroundContainer,
    backgroundImage: !!backgroundImage,
    hotspots: hotspots.length
  });

  if (!section || !canvas || !canvasContainer || !backgroundContainer || !backgroundImage) {
    console.error('❌ 关键元素未找到');
    return;
  }

  // 帧序列配置
  const frameCount = parseInt(canvas.getAttribute('data-frame-count')) || 244;
  const frameBaseUrl = canvas.getAttribute('data-frame-base-url') || '';
  const framePrefix = canvas.getAttribute('data-frame-prefix') || 'frame_';
  const frameSuffix = canvas.getAttribute('data-frame-suffix') || '.webp';

  console.log('⚙️ 帧配置:', {
    frameCount,
    frameBaseUrl,
    framePrefix,
    frameSuffix
  });

  // 帧序列播放器类
  class FrameSequencePlayer {
    constructor(canvas, frameCount, baseUrl, prefix, suffix) {
      this.canvas = canvas;
      this.ctx = canvas.getContext('2d');
      this.frameCount = frameCount;
      this.baseUrl = baseUrl;
      this.prefix = prefix;
      this.suffix = suffix;
      this.frames = [];
      this.loadedFrames = 0;
      this.currentFrame = 0;
      this.lastLoggedFrame = -1;
      this.isLoaded = false;
      this.duration = frameCount / 30; // 假设30fps
      this.backgroundLoaded = false;
      this.canvasSizeRetryCount = 0;
      this.maxRetries = 5;
      this.cacheCheckComplete = false;
      this.cachedFramesCount = 0;
      this.fastLoadMode = false;

      // 延迟初始化Canvas，确保DOM完全准备好
      this.initializeCanvas();
      this.checkCacheAndStart();
    }

    // 延迟初始化Canvas，确保DOM完全准备好
    initializeCanvas() {
      // 使用requestAnimationFrame确保DOM渲染完成
      requestAnimationFrame(() => {
        this.setupCanvas();
      });
    }

    setupCanvas() {
      const rect = this.canvas.getBoundingClientRect();
      const dpr = window.devicePixelRatio || 1;

      console.log('🖼️ 设置Canvas尺寸:', {
        width: rect.width,
        height: rect.height,
        dpr: dpr,
        retryCount: this.canvasSizeRetryCount
      });

      // 验证尺寸是否合理（最小100px）
      if (rect.width < 100 || rect.height < 100) {
        console.warn('⚠️ Canvas尺寸异常，准备重试:', { width: rect.width, height: rect.height });

        if (this.canvasSizeRetryCount < this.maxRetries) {
          this.canvasSizeRetryCount++;
          // 延迟重试，给DOM更多时间渲染
          setTimeout(() => {
            this.setupCanvas();
          }, 100 * this.canvasSizeRetryCount); // 递增延迟
          return;
        } else {
          console.error('❌ Canvas尺寸重试次数已达上限，使用视口尺寸');
          // 使用视口尺寸作为默认值
          rect.width = window.innerWidth;
          rect.height = window.innerHeight;
          console.log('🔧 使用视口尺寸:', { width: rect.width, height: rect.height });
        }
      }

      this.canvas.width = rect.width * dpr;
      this.canvas.height = rect.height * dpr;
      this.canvas.style.width = rect.width + 'px';
      this.canvas.style.height = rect.height + 'px';

      this.ctx.scale(dpr, dpr);
      this.ctx.imageSmoothingEnabled = true;
      this.ctx.imageSmoothingQuality = 'high';

      // 绘制初始背景
      this.ctx.fillStyle = '#0a0a0a';
      this.ctx.fillRect(0, 0, rect.width, rect.height);

      // 强制设置Canvas容器为全屏
      this.canvas.parentElement.style.width = '100%';
      this.canvas.parentElement.style.height = '100%';
      this.canvas.parentElement.style.position = 'absolute';
      this.canvas.parentElement.style.top = '0';
      this.canvas.parentElement.style.left = '0';

      console.log('✅ Canvas尺寸设置完成:', {
        canvasWidth: this.canvas.width,
        canvasHeight: this.canvas.height,
        styleWidth: this.canvas.style.width,
        styleHeight: this.canvas.style.height,
        containerWidth: rect.width,
        containerHeight: rect.height
      });
    }

    // 确保Canvas尺寸正确的方法
    ensureCorrectCanvasSize() {
      const rect = this.canvas.getBoundingClientRect();
      const currentStyleWidth = parseInt(this.canvas.style.width);
      const currentStyleHeight = parseInt(this.canvas.style.height);

      console.log('🔍 检查Canvas尺寸:', {
        containerRect: { width: rect.width, height: rect.height },
        canvasStyle: { width: currentStyleWidth, height: currentStyleHeight },
        canvasActual: { width: this.canvas.width, height: this.canvas.height }
      });

      // 检查当前尺寸是否与容器尺寸匹配，或者尺寸过小
      const needsResize = Math.abs(rect.width - currentStyleWidth) > 10 ||
                         Math.abs(rect.height - currentStyleHeight) > 10 ||
                         rect.width < 100 || rect.height < 100 ||
                         currentStyleWidth < 100 || currentStyleHeight < 100;

      if (needsResize) {
        console.log('🔄 检测到尺寸问题，强制重新设置Canvas尺寸');
        this.canvasSizeRetryCount = 0; // 重置重试计数

        // 强制等待一帧后再设置尺寸
        requestAnimationFrame(() => {
          this.setupCanvas();
          // 再次检查并强制重绘
          if (this.isLoaded && this.currentFrame >= 0) {
            console.log('🖼️ 尺寸调整后重绘当前帧:', this.currentFrame);
            this.drawFrame(this.currentFrame);
          }
        });
        return true;
      }
      return false;
    }

    // 检测缓存状态并决定加载策略
    async checkCacheAndStart() {
      console.log('🔍 开始检测帧图片缓存状态...');

      try {
        // 检测关键帧的缓存状态（第1帧、中间帧、最后帧）
        const keyFrames = [1, Math.floor(this.frameCount / 2), this.frameCount];
        let cachedCount = 0;

        // 检测移动端并使用对应素材
        const isMobile = window.innerWidth <= 768;
        let actualPrefix = this.prefix;
        let actualSuffix = this.suffix;

        if (isMobile) {
          actualPrefix = 'ucro5vhh_';
          actualSuffix = '.webp?v=1755487464';
        }

        // 设置总超时时间，避免无限等待
        const cacheCheckPromises = keyFrames.map(frameNum => {
          const frameUrl = `${this.baseUrl}${actualPrefix}${String(frameNum).padStart(4, '0')}${actualSuffix}`;
          return this.checkImageCache(frameUrl);
        });

        // 等待所有缓存检测完成，但设置总超时时间
        const results = await Promise.race([
          Promise.all(cacheCheckPromises),
          new Promise(resolve => setTimeout(() => resolve([false, false, false]), 500)) // 500ms超时
        ]);

        cachedCount = results.filter(Boolean).length;

        // 如果大部分关键帧都已缓存，启用快速加载模式
        this.fastLoadMode = cachedCount >= 2;
        this.cacheCheckComplete = true;

        console.log(`📊 缓存检测完成: ${cachedCount}/${keyFrames.length} 关键帧已缓存`, {
          fastLoadMode: this.fastLoadMode
        });

        if (this.fastLoadMode) {
          console.log('🚀 启用快速加载模式');
          // 快速模式：隐藏加载动画，直接开始加载
          this.hideLoadingIndicator();
        } else {
          console.log('⏳ 使用标准加载模式');
        }

      } catch (error) {
        console.warn('⚠️ 缓存检测失败，使用标准加载模式:', error);
        this.fastLoadMode = false;
        this.cacheCheckComplete = true;
      }

      // 无论缓存检测结果如何，都要开始预加载第一帧
      this.preloadFirstFrame();
    }

    // 检测单个图片是否已缓存
    checkImageCache(url) {
      return new Promise((resolve) => {
        const img = new Image();
        const startTime = performance.now();

        img.onload = () => {
          const loadTime = performance.now() - startTime;
          // 如果加载时间很短（<50ms），认为是缓存命中
          const isCached = loadTime < 50;
          resolve(isCached);
        };

        img.onerror = () => {
          resolve(false);
        };

        // 设置超时，避免长时间等待
        setTimeout(() => {
          resolve(false);
        }, 100);

        img.src = url;
      });
    }

    // 隐藏加载指示器
    hideLoadingIndicator() {
      if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
      }
    }

    // 预加载第一帧作为背景
    preloadFirstFrame() {
      // 检测移动端并使用对应素材
      const isMobile = window.innerWidth <= 768;
      let actualPrefix = this.prefix;
      let actualSuffix = this.suffix;

      if (isMobile) {
        actualPrefix = 'ucro5vhh_';
        actualSuffix = '.webp?v=1755487464';
      }

      const firstFrameUrl = `${this.baseUrl}${actualPrefix}0001${actualSuffix}`;
      console.log('🖼️ 预加载第一帧作为背景:', firstFrameUrl, isMobile ? '(移动端)' : '(桌面端)');

      backgroundImage.onload = () => {
        console.log('✅ 背景图片加载成功');
        this.backgroundLoaded = true;
        backgroundImage.style.display = 'block';

        if (this.fastLoadMode) {
          // 快速模式：立即开始加载帧序列
          console.log('🚀 快速模式：立即开始加载帧序列');
          this.loadFrames();
        } else {
          // 标准模式：更新加载进度，然后开始加载帧序列
          console.log('⏳ 标准模式：更新进度后开始加载');
          this.updateLoadingProgress();
          // 确保在标准模式下也开始加载帧序列
          this.loadFrames();
        }
      };

      backgroundImage.onerror = () => {
        console.error('❌ 背景图片加载失败');
        this.backgroundLoaded = false;
        // 如果背景加载失败，使用默认背景色
        backgroundContainer.style.background = '#0a0a0a';

        // 无论快速模式还是标准模式，都要开始加载帧序列
        console.log('⚠️ 背景加载失败，但继续加载帧序列');
        this.loadFrames();
      };

      backgroundImage.src = firstFrameUrl;
    }

    async loadFrames() {
      console.log(`开始加载 ${this.frameCount} 帧图片...`, {
        fastLoadMode: this.fastLoadMode
      });

      // 检测移动端并使用对应素材
      const isMobile = window.innerWidth <= 768;
      let actualPrefix = this.prefix;
      let actualSuffix = this.suffix;

      if (isMobile) {
        actualPrefix = 'ucro5vhh_';
        actualSuffix = '.webp?v=1755487464';
        console.log('📱 使用移动端优化素材:', actualPrefix);
      }

      const batchSize = this.fastLoadMode ? 15 : 10; // 快速模式使用更大批次
      const batches = [];

      for (let i = 0; i < this.frameCount; i += batchSize) {
        const batch = [];
        for (let j = i; j < Math.min(i + batchSize, this.frameCount); j++) {
          // 帧编号从1开始，不是从0开始
          const frameNumber = j + 1;
          const frameUrl = `${this.baseUrl}${actualPrefix}${String(frameNumber).padStart(4, '0')}${actualSuffix}`;
          batch.push(this.loadFrame(j, frameUrl));
        }
        batches.push(Promise.all(batch));
      }

      try {
        for (const batch of batches) {
          await batch;
          // 快速模式减少延迟
          const delay = this.fastLoadMode ? 5 : 10;
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        this.isLoaded = true;
        console.log('🎉 所有帧加载完成');

        // 根据模式决定过渡方式
        if (this.fastLoadMode) {
          // 快速模式：直接显示Canvas，无过渡动画
          this.startFastTransition();
        } else {
          // 标准模式：显示过渡动画
          this.startTransition();
        }

        // 确保Canvas尺寸正确后再绘制第一帧
        const sizeCorrect = this.ensureCorrectCanvasSize();
        if (sizeCorrect) {
          console.log('🔧 帧加载完成后已调整Canvas尺寸');
        }

        // 立即绘制第一帧并保持显示
        this.drawFrame(0);
        console.log('🖼️ 设置初始帧为第一帧');

        // 触发加载完成事件
        const event = new CustomEvent('framesLoaded', { detail: { sectionId } });
        document.dispatchEvent(event);

      } catch (error) {
        console.error('帧加载失败:', error);
        this.showError();
      }
    }

    loadFrame(index, url) {
      return new Promise((resolve) => {
        const img = new Image();
        img.crossOrigin = 'anonymous';

        img.onload = () => {
          this.frames[index] = img;
          this.loadedFrames++;
          this.updateLoadingProgress();
          resolve();
        };

        img.onerror = () => {
          console.warn(`❌ 帧 ${index + 1} 加载失败: ${url}`);
          this.frames[index] = null;
          this.loadedFrames++;
          this.updateLoadingProgress();
          resolve();
        };

        img.src = url;
      });
    }

    updateLoadingProgress() {
      // 快速模式不显示加载进度
      if (this.fastLoadMode) return;

      // 计算总进度（包括背景图片）
      const backgroundProgress = this.backgroundLoaded ? 1 : 0;
      const framesProgress = this.loadedFrames;
      const totalProgress = (backgroundProgress + framesProgress) / (this.frameCount + 1) * 100;

      if (loadingBar) {
        loadingBar.style.width = totalProgress + '%';
      }

      console.log(`📊 加载进度: 背景${this.backgroundLoaded ? '✅' : '⏳'} 帧${this.loadedFrames}/${this.frameCount} 总进度${totalProgress.toFixed(1)}%`);
    }

    // 开始过渡动画
    startTransition() {
      console.log('🎭 开始过渡动画');

      // 在过渡开始前确保Canvas尺寸正确
      const sizeAdjusted = this.ensureCorrectCanvasSize();
      if (sizeAdjusted) {
        console.log('🔧 过渡前已调整Canvas尺寸');
        // 如果尺寸被调整，重新绘制当前帧
        if (this.frames[0]) {
          this.drawFrame(0);
        }
      }

      if (loadingIndicator) {
        loadingIndicator.classList.add('hidden');
      }

      // 延迟执行过渡，确保加载指示器完全隐藏
      setTimeout(() => {
        // 显示Canvas容器
        if (canvasContainer) {
          canvasContainer.classList.add('loaded');
        }

        // 再次确保Canvas尺寸正确（在容器显示后）
        setTimeout(() => {
          const finalSizeCheck = this.ensureCorrectCanvasSize();
          if (finalSizeCheck) {
            console.log('🔧 容器显示后已调整Canvas尺寸');
            // 重新绘制第一帧确保正确显示
            if (this.frames[0]) {
              this.drawFrame(0);
            }
          }
        }, 100);

        // 延迟隐藏背景图片，确保Canvas完全显示后再隐藏
        setTimeout(() => {
          if (backgroundContainer) {
            backgroundContainer.classList.add('hidden');
          }
        }, 400); // 等待Canvas淡入一半时间后开始隐藏背景

      }, 300); // 等待加载指示器隐藏动画完成
    }

    // 快速过渡模式（无加载动画）
    startFastTransition() {
      console.log('🚀 开始快速过渡（无加载动画）');

      // 多次确保Canvas尺寸正确
      const performSizeCheck = () => {
        const sizeAdjusted = this.ensureCorrectCanvasSize();
        if (sizeAdjusted) {
          console.log('🔧 快速过渡中已调整Canvas尺寸');
        }

        // 强制重绘第一帧
        if (this.frames[0]) {
          console.log('🖼️ 快速过渡中重绘第一帧');
          this.drawFrame(0);
        }
      };

      // 立即检查一次
      performSizeCheck();

      // 直接显示Canvas容器
      if (canvasContainer) {
        canvasContainer.classList.add('loaded');
        canvasContainer.style.opacity = '1';
      }

      // 容器显示后再次检查尺寸
      setTimeout(() => {
        console.log('🔄 快速过渡后延迟尺寸检查');
        performSizeCheck();
      }, 50);

      // 立即隐藏背景图片
      if (backgroundContainer) {
        backgroundContainer.classList.add('hidden');
      }

      // 最终检查
      setTimeout(() => {
        console.log('🔄 快速过渡最终尺寸检查');
        performSizeCheck();
      }, 200);

      console.log('✅ 快速过渡完成');
    }

    showError() {
      console.error('❌ 帧序列加载失败');
      if (loadingIndicator) {
        const text = loadingIndicator.querySelector('.ir3-leveling-frames__loading-text');
        if (text) {
          text.textContent = 'Failed to load frames. Please refresh.';
          text.style.color = '#ff6b6b';
        }
      }

      // 绘制错误占位图
      this.drawErrorPlaceholder();
    }

    drawErrorPlaceholder() {
      const canvasWidth = this.canvas.width / (window.devicePixelRatio || 1);
      const canvasHeight = this.canvas.height / (window.devicePixelRatio || 1);

      this.ctx.fillStyle = '#1a1a1a';
      this.ctx.fillRect(0, 0, canvasWidth, canvasHeight);

      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '24px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('Frame Loading Failed', canvasWidth / 2, canvasHeight / 2);
      this.ctx.fillText('Please check console for details', canvasWidth / 2, canvasHeight / 2 + 30);
    }

    drawFrame(frameIndex) {
      if (!this.isLoaded || frameIndex < 0 || frameIndex >= this.frameCount) {
        return false;
      }

      const frame = this.frames[frameIndex];
      if (!frame) {
        return false;
      }

      const canvasWidth = this.canvas.width / (window.devicePixelRatio || 1);
      const canvasHeight = this.canvas.height / (window.devicePixelRatio || 1);

      // 强制清除整个Canvas
      this.ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      // 计算缩放和居中 (cover模式)
      const canvasAspect = canvasWidth / canvasHeight;
      const imageAspect = frame.width / frame.height;

      let drawWidth, drawHeight, drawX, drawY;

      if (canvasAspect > imageAspect) {
        drawWidth = canvasWidth;
        drawHeight = drawWidth / imageAspect;
        drawX = 0;
        drawY = (canvasHeight - drawHeight) / 2;
      } else {
        drawHeight = canvasHeight;
        drawWidth = drawHeight * imageAspect;
        drawX = (canvasWidth - drawWidth) / 2;
        drawY = 0;
      }

      // 保存Canvas实际渲染区域信息
      canvasRenderInfo.width = drawWidth;
      canvasRenderInfo.height = drawHeight;
      canvasRenderInfo.offsetX = drawX;
      canvasRenderInfo.offsetY = drawY;

      // 强制重绘
      this.ctx.save();
      this.ctx.drawImage(frame, drawX, drawY, drawWidth, drawHeight);
      this.ctx.restore();

      this.currentFrame = frameIndex;

      // 记录当前帧，用于调试
      this.lastLoggedFrame = frameIndex;

      // 🔍 更新调试信息
      if (showDebugMode) {
        const stage = this.stage1Playing ? '第一阶段播放中' :
                     this.stage2Active ? '第二阶段滚动控制' :
                     this.stage1Completed ? '第一阶段完成' : '未开始';
        updateDebugInfo(frameIndex, stage, frameIndex / this.frameCount, currentActiveHotspot);
      }

      return true;
    }

    setProgress(progress) {
      if (!this.isLoaded) return;

      // 确保progress在0-1范围内
      progress = Math.max(0, Math.min(1, progress));

      // 映射到完整帧范围：0% -> 帧0, 100% -> 帧243 (总共244帧)
      const frameIndex = Math.floor(progress * (this.frameCount - 1));

      // 强制更新帧
      this.drawFrame(frameIndex);
    }

    getCurrentTime() {
      return (this.currentFrame / this.frameCount) * this.duration;
    }
  }

  // 创建帧序列播放器
  const framePlayer = new FrameSequencePlayer(canvas, frameCount, frameBaseUrl, framePrefix, frameSuffix);

  // 注意：不在这里调用loadFrames()，因为已经在checkCacheAndStart()中处理了

  // 等待帧加载完成后初始化ScrollTrigger
  document.addEventListener('framesLoaded', function(event) {
    if (event.detail.sectionId === sectionId) {
      // 在ScrollTrigger初始化前再次确保Canvas尺寸正确
      console.log('📐 ScrollTrigger初始化前最终尺寸检查');
      setTimeout(() => {
        const finalSizeCheck = framePlayer.ensureCorrectCanvasSize();
        if (finalSizeCheck) {
          console.log('🔧 ScrollTrigger初始化前已调整Canvas尺寸');
        }
        setupScrollTrigger();
      }, 100);
    }
  });

  function setupScrollTrigger() {
    console.log('🎬 设置ScrollTrigger for section:', sectionId);

    // 修复：添加全局滚动状态管理
    if (!window.scrollControllers) {
      window.scrollControllers = new Set();
    }

    // 注册当前组件的滚动控制器
    const controllerId = `auto-leveling-frames-${sectionId}`;
    window.scrollControllers.add(controllerId);

    // 修复：添加滚动冲突检测函数
    function checkScrollConflicts() {
      // 检查是否有其他组件的滚动锁定激活
      const hasScrollLock = document.body.classList.contains('scroll-locked');
      const hasFixedPosition = document.body.style.position === 'fixed';

      if (hasScrollLock || hasFixedPosition) {
        console.log('⚠️ 检测到其他组件的滚动锁定，正在清理...');
        // 强制清理其他组件的滚动锁定
        document.body.classList.remove('scroll-locked');
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.left = '';
        document.body.style.right = '';
        document.body.style.width = '';
        document.body.style.overflow = '';
        return true;
      }
      return false;
    }

    // 初始化标题显示状态 - 设为隐藏，等待动画触发
    if (titleElement) {
      titleElement.style.opacity = 0;
      titleElement.style.transform = 'translate(-50%, -50%) translateY(20px)';
      titleElement.classList.remove('visible');
      console.log('📝 初始化标题为隐藏状态');
    }

    // 🎬 预触发ScrollTrigger - 处理标题提前出现
    const preTrigger = ScrollTrigger.create({
      trigger: section,
      start: 'top center',
      end: 'top top',
      onEnter: () => {
        // 修复：进入前清理其他组件的滚动状态
        console.log('🧹 预触发阶段：清理其他组件的滚动锁定状态');

        // 调用其他组件的解锁函数
        if (window.unlockScroll && typeof window.unlockScroll === 'function') {
          console.log('🔓 预触发：释放Smart Features滚动锁定');
          window.unlockScroll();
        }
        if (window.disableScrollLock && typeof window.disableScrollLock === 'function') {
          console.log('🔓 预触发：释放Key Features滚动锁定');
          window.disableScrollLock();
        }

        // 强制清理body样式
        if (document.body.classList.contains('scroll-locked')) {
          document.body.classList.remove('scroll-locked');
          document.body.style.position = '';
          document.body.style.top = '';
          document.body.style.left = '';
          document.body.style.right = '';
          document.body.style.width = '';
          document.body.style.overflow = '';
        }
      },
      onUpdate: (self) => {
        if (titleElement) {
          // 在预触发阶段，根据进度控制标题淡入
          const preProgress = self.progress;
          if (preProgress > 0) {
            const fadeInProgress = Math.min(1, preProgress);
            titleElement.style.opacity = fadeInProgress;
            titleElement.style.transform = `translate(-50%, -50%) translateY(${20 * (1 - fadeInProgress)}px)`;
            titleElement.classList.add('visible');
          }
        }
      }
    });

    // 修复：在创建ScrollTrigger前检查并清理冲突
    const hasConflicts = checkScrollConflicts();
    if (hasConflicts) {
      console.log('✅ 滚动冲突已清理，继续创建ScrollTrigger');
    }

    // 修复滚动显示异常：优化ScrollTrigger设置
    const scrollTrigger = ScrollTrigger.create({
      trigger: section,
      start: 'top top',
      end: '+=300%',
      pin: true,
      pinSpacing: true, // 修复：确保正确的空间计算，避免白色空白
      scrub: 1,
      anticipatePin: 1, // 修复：提前准备pin，减少视觉跳跃
      onUpdate: (self) => {
        const progress = Math.max(0, Math.min(1, self.progress));

        // 更新帧播放 - 确保映射到完整帧范围
        framePlayer.setProgress(progress);

        // 强制Canvas重绘以确保帧更新
        if (framePlayer.isLoaded) {
          const currentFrameIndex = Math.floor(progress * (framePlayer.frameCount - 1));
          if (framePlayer.frames[currentFrameIndex]) {
            framePlayer.drawFrame(currentFrameIndex);
          }
        }

        // 标题显示控制 - 主ScrollTrigger阶段，标题已经通过预触发显示
        if (titleElement) {
          if (progress < 0.20) {
            // 完全显示：0-20%进度时保持完全可见（标题已通过预触发淡入）
            titleElement.style.opacity = 1;
            titleElement.style.transform = 'translate(-50%, -50%) translateY(0px)';
            titleElement.classList.add('visible');
          } else if (progress < 0.25) {
            // 出场动画：20-25%进度时快速淡出
            const fadeOutProgress = (progress - 0.20) / 0.05;
            titleElement.style.opacity = 1 - fadeOutProgress;
            titleElement.style.transform = `translate(-50%, -50%) translateY(${-20 * fadeOutProgress}px)`;
          } else {
            // 完全隐藏
            titleElement.style.opacity = 0;
            titleElement.classList.remove('visible');
          }
        }

        // 热点和文本控制
        updateHotspotsAndText(progress);
      },
      onEnter: () => {
        console.log('🎯 进入Auto Leveling Frames ScrollTrigger区域');
        // 修复：确保其他组件的滚动锁定被正确释放
        if (window.unlockScroll && typeof window.unlockScroll === 'function') {
          console.log('🔓 释放Smart Features滚动锁定');
          window.unlockScroll();
        }
        if (window.disableScrollLock && typeof window.disableScrollLock === 'function') {
          console.log('🔓 释放Key Features滚动锁定');
          window.disableScrollLock();
        }
      },
      onLeave: () => {
        console.log('🚪 离开Auto Leveling Frames ScrollTrigger区域');
        // 修复：离开时清理状态，防止白色空白区域
        if (window.scrollControllers) {
          window.scrollControllers.delete(controllerId);
        }
      },
      onEnterBack: () => {
        console.log('🔄 重新进入Auto Leveling Frames区域');
        // 确保其他组件的滚动锁定被释放
        if (window.unlockScroll && typeof window.unlockScroll === 'function') {
          window.unlockScroll();
        }
        // 重新注册控制器
        if (window.scrollControllers) {
          window.scrollControllers.add(controllerId);
        }
      }
    });

    console.log('✅ ScrollTrigger创建成功');
  }

  function updateHotspotsAndText(progress) {
    let activeHotspotIndex = -1;

    // 获取容器尺寸
    const containerRect = section.getBoundingClientRect();
    const hotspotsContainer = document.getElementById('ir3-hotspots-' + sectionId);

    // 计算实际的Canvas渲染区域相对于容器的位置
    if (canvasRenderInfo.width > 0) {
      // 设置热点容器的大小和位置以匹配Canvas实际渲染区域
      hotspotsContainer.style.width = canvasRenderInfo.width + 'px';
      hotspotsContainer.style.height = canvasRenderInfo.height + 'px';
    }

    // 检查哪个热点应该激活
    hotspots.forEach((hotspot, index) => {
      const startTime = parseFloat(hotspot.getAttribute('data-start-time')) / 100;
      const endTime = parseFloat(hotspot.getAttribute('data-end-time')) / 100;

      if (progress >= startTime && progress <= endTime) {
        activeHotspotIndex = index;
        hotspot.classList.add('active');
      } else {
        hotspot.classList.remove('active');
      }
    });

    // 更新文本面板
    if (activeHotspotIndex >= 0 && activeHotspotIndex !== currentActiveHotspot) {
      const activeHotspot = hotspots[activeHotspotIndex];
      const title = activeHotspot.getAttribute('data-title');
      const description = activeHotspot.getAttribute('data-description');

      if (title && description) {
        textTitle.textContent = title;
        textDescription.textContent = description;

        // 定位文本面板到热点附近
        positionTextPanel(activeHotspot);
        textPanel.classList.add('active');
      }

      currentActiveHotspot = activeHotspotIndex;
    } else if (activeHotspotIndex === -1) {
      textPanel.classList.remove('active');
      currentActiveHotspot = -1;
    }
  }

  function positionTextPanel(hotspot) {
    // 获取热点的实际位置
    const hotspotRect = hotspot.getBoundingClientRect();
    const containerRect = section.getBoundingClientRect();
    
    // 计算热点中心点在视口中的位置
    const hotspotCenterX = hotspotRect.left + hotspotRect.width / 2;
    const hotspotCenterY = hotspotRect.top + hotspotRect.height / 2;
    
    // 获取文本面板宽度（响应式）
    const isMobile = window.innerWidth <= 768;
    const panelWidth = isMobile ? 240 : 280;
    const panelHeight = 120; // 预估高度
    
    // 固定像素距离（响应式调整）
    const FIXED_DISTANCE = isMobile ? 40 : 60; // 移动端使用更小的距离
    const HOTSPOT_RADIUS = isMobile ? 12 : 16; // 热点半径
    
    let panelX, panelY;
    
    // 智能定位：根据热点在屏幕中的位置决定文本面板显示位置
    const screenCenterX = window.innerWidth / 2;
    const screenCenterY = window.innerHeight / 2;
    
    // 水平定位
    if (hotspotCenterX < screenCenterX) {
      // 热点在左半屏，文本显示在右侧
      panelX = hotspotCenterX + HOTSPOT_RADIUS + FIXED_DISTANCE;
      
      // 边界检查
      if (panelX + panelWidth > window.innerWidth - 10) {
        panelX = window.innerWidth - panelWidth - 10;
      }
    } else {
      // 热点在右半屏，文本显示在左侧
      panelX = hotspotCenterX - HOTSPOT_RADIUS - FIXED_DISTANCE - panelWidth;
      
      // 边界检查
      if (panelX < 10) {
        panelX = 10;
      }
    }
    
    // 垂直定位：优先显示在上方
    panelY = hotspotCenterY - panelHeight - (isMobile ? 20 : 30);
    
    // 边界检查
    if (panelY < 10) {
      // 如果上方空间不足，显示在下方
      panelY = hotspotCenterY + (isMobile ? 20 : 30);
    }
    
    if (panelY + panelHeight > window.innerHeight - 10) {
      // 如果下方空间也不足，显示在同一水平线
      panelY = hotspotCenterY - panelHeight / 2;
    }
    
    // 应用位置（使用fixed定位，单位为px）
    textPanel.style.left = panelX + 'px';
    textPanel.style.top = panelY + 'px';
    textPanel.style.transform = 'none';
  }

  // 窗口大小变化处理
  const debouncedResize = debounce(() => {
    // 重置重试计数，允许重新设置Canvas尺寸
    framePlayer.canvasSizeRetryCount = 0;
    framePlayer.setupCanvas();
    if (framePlayer.isLoaded) {
      framePlayer.drawFrame(framePlayer.currentFrame);
    }

    // 重新计算当前激活热点的文本面板位置
    if (currentActiveHotspot >= 0 && currentActiveHotspot < hotspots.length) {
      const activeHotspot = hotspots[currentActiveHotspot];
      if (textPanel.classList.contains('active')) {
        positionTextPanel(activeHotspot);
      }
    }

    ScrollTrigger.refresh();
  }, 200);

  window.addEventListener('resize', debouncedResize);

  // 🔍 初始化调试模式
  function initDebugMode() {
    console.log('🔍 初始化调试模式');

    // 鼠标跟踪
    section.addEventListener('mousemove', function(e) {
      const rect = section.getBoundingClientRect();
      const x = ((e.clientX - rect.left) / rect.width * 100).toFixed(1);
      const y = ((e.clientY - rect.top) / rect.height * 100).toFixed(1);

      if (debugElements.mouseCoords) {
        debugElements.mouseCoords.textContent = `X: ${x}%, Y: ${y}%`;
        debugElements.mouseCoords.style.left = (e.clientX - rect.left + 10) + 'px';
        debugElements.mouseCoords.style.top = (e.clientY - rect.top - 30) + 'px';
      }

      if (debugElements.debugMouse) {
        debugElements.debugMouse.textContent = `X: ${x}%, Y: ${y}%`;
      }
    });

    // 更新Canvas区域轮廓
    function updateCanvasOutline() {
      if (canvasRenderInfo.width > 0 && debugElements.canvasOutline) {
        const containerRect = section.getBoundingClientRect();
        debugElements.canvasOutline.style.left = canvasRenderInfo.offsetX + 'px';
        debugElements.canvasOutline.style.top = canvasRenderInfo.offsetY + 'px';
        debugElements.canvasOutline.style.width = canvasRenderInfo.width + 'px';
        debugElements.canvasOutline.style.height = canvasRenderInfo.height + 'px';

        if (debugElements.debugCanvas) {
          debugElements.debugCanvas.textContent = `${canvasRenderInfo.width}x${canvasRenderInfo.height}`;
        }
      }
    }

    // 定期更新Canvas轮廓
    setInterval(updateCanvasOutline, 1000);
  }

  // 🔍 更新调试信息
  function updateDebugInfo(frameIndex, stage, progress, activeHotspot) {
    if (!showDebugMode) return;

    if (debugElements.debugFrame) {
      debugElements.debugFrame.textContent = `${frameIndex} / ${framePlayer ? framePlayer.frameCount : 244}`;
    }

    if (debugElements.debugStage) {
      debugElements.debugStage.textContent = stage;
    }

    if (debugElements.debugProgress) {
      debugElements.debugProgress.textContent = `${(progress * 100).toFixed(1)}%`;
    }

    if (debugElements.debugHotspot) {
      debugElements.debugHotspot.textContent = activeHotspot >= 0 ? `热点 ${activeHotspot + 1}` : '无';
    }
  }

  // 防抖函数
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // 修复：增强的清理函数
  function cleanup() {
    console.log('🧹 清理Auto Leveling Frames组件');

    // 清理ScrollTrigger
    ScrollTrigger.getAll().forEach(trigger => {
      if (trigger.trigger === section) {
        trigger.kill();
      }
    });

    // 清理全局滚动状态
    if (window.scrollControllers) {
      window.scrollControllers.delete(controllerId);
    }

    // 确保body样式被清理
    document.body.classList.remove('scroll-locked');
    document.body.style.position = '';
    document.body.style.top = '';
    document.body.style.left = '';
    document.body.style.right = '';
    document.body.style.width = '';
    document.body.style.overflow = '';

    window.removeEventListener('resize', debouncedResize);
  }

  // 修复：页面级别的滚动状态重置
  function resetGlobalScrollState() {
    console.log('🔄 重置全局滚动状态');
    document.body.classList.remove('scroll-locked');
    document.body.style.position = '';
    document.body.style.top = '';
    document.body.style.left = '';
    document.body.style.right = '';
    document.body.style.width = '';
    document.body.style.overflow = '';

    // 清理所有滚动控制器
    if (window.scrollControllers) {
      window.scrollControllers.clear();
    }
  }

  // 页面加载时重置状态
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', resetGlobalScrollState);
  } else {
    resetGlobalScrollState();
  }

  // Shopify主题编辑器支持
  if (window.Shopify && window.Shopify.designMode) {
    document.addEventListener('shopify:section:unload', function(event) {
      if (event.detail.sectionId === sectionId) {
        cleanup();
      }
    });
  }

  // 页面卸载时清理
  window.addEventListener('beforeunload', cleanup);

  // 开始初始化
  checkCacheAndStart();
}
</script>

{% schema %}
{
  "name": "IR3 V2 智能调平帧动画",
  "settings": [
    {
      "type": "header",
      "content": "基本设置"
    },
    {
      "type": "checkbox",
      "id": "show_title",
      "label": "显示主标题",
      "default": true
    },
    {
      "type": "text",
      "id": "main_title",
      "label": "主标题",
      "default": "IR3 V2 Smart Leveling System"
    },
    {
      "type": "textarea",
      "id": "main_description",
      "label": "主描述",
      "default": "6-point precise detection, 0.01mm ultra-high precision, ensuring every print is flawless."
    },
    {
      "type": "header",
      "content": "帧序列设置"
    },
    {
      "type": "number",
      "id": "frame_count",
      "label": "帧数量",
      "default": 232
    },
    {
      "type": "text",
      "id": "frame_base_url",
      "label": "帧图片基础URL",
      "default": "https://cdn.shopify.com/s/files/1/0762/6113/0493/files/"
    },
    {
      "type": "text",
      "id": "frame_prefix",
      "label": "帧文件前缀",
      "default": "frame_"
    },
    {
      "type": "text",
      "id": "frame_suffix",
      "label": "帧文件后缀",
      "default": ".webp"
    },
    {
      "type": "header",
      "content": "样式设置"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "背景颜色",
      "default": "#0a0a0a"
    },
    {
      "type": "range",
      "id": "section_height_desktop",
      "label": "桌面端高度 (px)",
      "min": 600,
      "max": 2000,
      "step": 50,
      "default": 1200
    },
    {
      "type": "range",
      "id": "section_height_mobile",
      "label": "移动端高度 (px)",
      "min": 300,
      "max": 800,
      "step": 25,
      "default": 400
    },
    {
      "type": "range",
      "id": "title_size",
      "label": "标题字号 (px)",
      "min": 24,
      "max": 72,
      "step": 2,
      "default": 48
    },
    {
      "type": "range",
      "id": "description_size",
      "label": "描述字号 (px)",
      "min": 14,
      "max": 32,
      "step": 1,
      "default": 20
    },
    {
      "type": "header",
      "content": "🔍 调试模式"
    },
    {
      "type": "checkbox",
      "id": "show_debug_mode",
      "label": "启用调试模式",
      "info": "显示网格、Canvas区域轮廓、鼠标坐标和调试信息面板",
      "default": false
    },
    {
      "type": "header",
      "content": "热点 1 设置"
    },
    {
      "type": "text",
      "id": "hotspot_1_title",
      "label": "热点 1 标题",
      "default": "Initial Position Detection"
    },
    {
      "type": "textarea",
      "id": "hotspot_1_description",
      "label": "热点 1 描述",
      "default": "The print head moves to the front of the conveyor belt, preparing to start the smart leveling program"
    },
    {
      "type": "range",
      "id": "hotspot_1_x",
      "label": "热点 1 X位置 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 82
    },
    {
      "type": "range",
      "id": "hotspot_1_y",
      "label": "热点 1 Y位置 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 64
    },
    {
      "type": "range",
      "id": "hotspot_1_start_time",
      "label": "热点 1 开始时间 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 23
    },
    {
      "type": "range",
      "id": "hotspot_1_end_time",
      "label": "热点 1 结束时间 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 30
    },
    {
      "type": "header",
      "content": "热点 2 设置"
    },
    {
      "type": "text",
      "id": "hotspot_2_title",
      "label": "热点 2 标题",
      "default": "Precise detection of contact"
    },
    {
      "type": "textarea",
      "id": "hotspot_2_description",
      "label": "热点 2 描述",
      "default": "The print head descends slowly, detecting the precise height position of the conveyor belt surface"
    },
    {
      "type": "range",
      "id": "hotspot_2_x",
      "label": "热点 2 X位置 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 76
    },
    {
      "type": "range",
      "id": "hotspot_2_y",
      "label": "热点 2 Y位置 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 64
    },
    {
      "type": "range",
      "id": "hotspot_2_start_time",
      "label": "热点 2 开始时间 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 31
    },
    {
      "type": "range",
      "id": "hotspot_2_end_time",
      "label": "热点 2 结束时间 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 40
    },
    {
      "type": "header",
      "content": "热点 3 设置"
    },
    {
      "type": "text",
      "id": "hotspot_3_title",
      "label": "热点 3 标题",
      "default": "Y-axis offset calculation"
    },
    {
      "type": "textarea",
      "id": "hotspot_3_description",
      "label": "热点 3 描述",
      "default": "The system records and calculates the offset on the Y-axis, preparing for compensation adjustments"
    },
    {
      "type": "range",
      "id": "hotspot_3_x",
      "label": "热点 3 X位置 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 62
    },
    {
      "type": "range",
      "id": "hotspot_3_y",
      "label": "热点 3 Y位置 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 63
    },
    {
      "type": "range",
      "id": "hotspot_3_start_time",
      "label": "热点 3 开始时间 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 42
    },
    {
      "type": "range",
      "id": "hotspot_3_end_time",
      "label": "热点 3 结束时间 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 49
    },
    {
      "type": "header",
      "content": "热点 4 设置"
    },
    {
      "type": "text",
      "id": "hotspot_4_title",
      "label": "热点 4 标题",
      "default": "Multi-point accuracy verification"
    },
    {
      "type": "textarea",
      "id": "hotspot_4_description",
      "label": "热点 4 描述",
      "default": "Conduct multiple contact tests along the conveyor belt to ensure leveling accuracy reaches 0.01mm."
    },
    {
      "type": "range",
      "id": "hotspot_4_x",
      "label": "热点 4 X位置 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 46
    },
    {
      "type": "range",
      "id": "hotspot_4_y",
      "label": "热点 4 Y位置 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 63
    },
    {
      "type": "range",
      "id": "hotspot_4_start_time",
      "label": "热点 4 开始时间 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 52
    },
    {
      "type": "range",
      "id": "hotspot_4_end_time",
      "label": "热点 4 结束时间 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 60
    },
    {
      "type": "header",
      "content": "热点 5 设置"
    },
    {
      "type": "text",
      "id": "hotspot_5_title",
      "label": "热点 5 标题",
      "default": "Intelligent Compensation Application"
    },
    {
      "type": "textarea",
      "id": "hotspot_5_description",
      "label": "热点 5 描述",
      "default": "The system automatically applies the Y-axis compensation value to achieve a perfect printing plane"
    },
    {
      "type": "range",
      "id": "hotspot_5_x",
      "label": "热点 5 X位置 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 30
    },
    {
      "type": "range",
      "id": "hotspot_5_y",
      "label": "热点 5 Y位置 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 62
    },
    {
      "type": "range",
      "id": "hotspot_5_start_time",
      "label": "热点 5 开始时间 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 63
    },
    {
      "type": "range",
      "id": "hotspot_5_end_time",
      "label": "热点 5 结束时间 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 69
    },
    {
      "type": "header",
      "content": "热点 6 设置"
    },
    {
      "type": "text",
      "id": "hotspot_6_title",
      "label": "热点 6 标题",
      "default": "Leveling completion confirmation"
    },
    {
      "type": "textarea",
      "id": "hotspot_6_description",
      "label": "热点 6 描述",
      "default": "The 6-point intelligent leveling is complete, and the system is ready for high-precision 45° angle printing"
    },
    {
      "type": "range",
      "id": "hotspot_6_x",
      "label": "热点 6 X位置 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 13
    },
    {
      "type": "range",
      "id": "hotspot_6_y",
      "label": "热点 6 Y位置 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 61
    },
    {
      "type": "range",
      "id": "hotspot_6_start_time",
      "label": "热点 6 开始时间 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 73
    },
    {
      "type": "range",
      "id": "hotspot_6_end_time",
      "label": "热点 6 结束时间 (%)",
      "min": 0,
      "max": 100,
      "step": 1,
      "default": 81
    }
  ],
  "presets": [
    {
      "name": "IR3 V2 智能调平帧动画"
    }
  ]
}
{% endschema %}