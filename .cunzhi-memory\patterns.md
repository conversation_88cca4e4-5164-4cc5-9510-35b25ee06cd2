# 常用模式和最佳实践

- Shopify导航栏搜索功能完整实现模式：1)集成现有PredictiveSearch系统而非重新开发 2)使用js-search-header类触发predictive-search:open事件 3)搜索容器需要site-header__search-container类配合原生功能 4)搜索按钮定位使用top/bottom+margin:auto比transform更可靠 5)高亮文字颜色需要强制覆盖Shopify的styled_text内联样式 6)热门搜索需要与搜索框保持相同的max-width和居中对齐
- 优惠码验证逻辑修复完成：1)修复了401/502错误时错误返回resolve(true)的问题，改为resolve(null)跳过验证 2)添加了fetch请求headers防止浏览器弹出HTTP基本认证对话框：Authorization: 'Bearer dummy', X-Requested-With: 'XMLHttpRequest', credentials: 'omit', mode: 'cors' 3)现在系统能正确处理无效优惠码，不会误导用户显示成功应用折扣 4)在开发环境中优雅降级，让Shopify后端处理验证
- 购物车优惠码验证问题：使用/discount/CODE?redirect=/cart方法在生产环境中不会返回明确的错误反馈，只是刷新页面。Shopify结算页面使用GraphQL API (/checkouts/unstable/graphql?operationName=Proposal) 来处理优惠码验证，这是更可靠的方法。需要考虑使用Storefront API或其他方法来实现真正的优惠码验证反馈。
- GraphQL折扣码验证功能完全成功实现：1)使用Shopify GraphQL Storefront API替代不可靠的REST API 2)配置访问令牌8eccc187c638e7ebecf615ccf66fb7e8 3)通过Context7 MCP查询官方文档获得准确的GraphQL语法 4)实现cartDiscountCodesUpdate mutation进行折扣码应用和清空 5)RPNW09XGXDPN折扣码验证完全工作，显示-$8.99折扣 6)checkout页面正确传递折扣码参数 7)价格计算准确：$890.01(原价$899.00-折扣$8.99) 8)GraphQL返回准确状态applicable:true 9)实现了清空折扣码功能使用空数组参数 10)提供URL跳转清空方法作为备选方案
