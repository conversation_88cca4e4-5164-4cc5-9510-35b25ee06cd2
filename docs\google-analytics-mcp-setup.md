# Google Analytics MCP 完整配置指南

本文档详细介绍如何从零开始配置和使用 Google Analytics MCP Server。官方文档过于简略，本指南将提供完整的操作步骤。

## 什么是 MCP 和 Gemini CLI？

**MCP (Model Context Protocol)** 是一个协议，允许 AI 助手连接外部工具和数据源。
**Gemini CLI** 是 Google 的命令行 AI 助手，支持通过 MCP 服务器扩展功能。

Google Analytics MCP Server 让 Gemini CLI 能够：
- 查询你的 Google Analytics 数据
- 生成报告和分析
- 获取账户和属性信息
- 运行实时报告

## 完整安装流程

### 第一步：安装 Gemini CLI

Gemini CLI 是使用 MCP 服务器的前提。

#### 方法1：使用 npm 安装（推荐）
```bash
# 全局安装 Gemini CLI
npm install -g @google/gemini-cli

# 验证安装
gemini --version
```

#### 方法2：使用 Homebrew（macOS/Linux）
```bash
brew install gemini-cli
```

#### 方法3：直接运行（无需安装）
```bash
npx https://github.com/google-gemini/gemini-cli
```

### 第二步：安装 pipx

pipx 用于运行 Google Analytics MCP Server：

```bash
# Windows/Linux/macOS 通用方法
python -m pip install --user pipx

# Ubuntu/Debian
sudo apt install pipx

# macOS
brew install pipx

# 验证安装
pipx --version
```

### 第三步：创建 Google Cloud 项目并启用 API

#### 1. 创建 Google Cloud 项目
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 点击项目选择器，然后点击"新建项目"
3. 输入项目名称，记住项目 ID（后面会用到）
4. 点击"创建"

#### 2. 启用必要的 API
在你的 Google Cloud 项目中启用以下 API：

1. 访问 [Google Analytics Admin API](https://console.cloud.google.com/apis/library/analyticsadmin.googleapis.com)
   - 确保选择了正确的项目
   - 点击"启用"按钮

2. 访问 [Google Analytics Data API](https://console.cloud.google.com/apis/library/analyticsdata.googleapis.com)
   - 点击"启用"按钮

### 第四步：配置身份验证（重要！）

这是最关键的步骤，有两种方法：

#### 方法A：OAuth 客户端认证（推荐个人使用）

1. **创建 OAuth 客户端**：
   - 访问 [Google Cloud Console 凭据页面](https://console.cloud.google.com/apis/credentials)
   - 点击"创建凭据" → "OAuth 客户端 ID"
   - 应用类型选择"桌面应用程序"
   - 输入名称（如"Analytics MCP Client"）
   - 点击"创建"

2. **下载凭据文件**：
   - 点击下载按钮，保存 JSON 文件到安全位置
   - 记住文件路径，例如：`/Users/<USER>/credentials.json`

3. **安装 Google Cloud CLI**：
   ```bash
   # macOS
   brew install google-cloud-sdk

   # Windows - 下载安装包
   # https://cloud.google.com/sdk/docs/install

   # Ubuntu/Debian
   sudo apt-get install google-cloud-cli
   ```

4. **设置认证**：
   ```bash
   # 使用下载的凭据文件进行认证
   gcloud auth application-default login \
     --scopes https://www.googleapis.com/auth/analytics.readonly,https://www.googleapis.com/auth/cloud-platform \
     --client-id-file=/path/to/your/credentials.json
   ```

#### 方法B：服务账户认证（推荐企业使用）

1. **创建服务账户**：
   - 访问 [服务账户页面](https://console.cloud.google.com/iam-admin/serviceaccounts)
   - 点击"创建服务账户"
   - 输入名称和描述
   - 点击"创建并继续"

2. **分配权限**：
   - 添加角色："Viewer" 或 "Analytics Viewer"
   - 点击"继续"

3. **创建密钥**：
   - 点击创建的服务账户
   - 转到"密钥"标签
   - 点击"添加密钥" → "创建新密钥"
   - 选择 JSON 格式，下载文件

4. **设置环境变量**：
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account-key.json"
   ```

### 第五步：配置 Gemini CLI 的 MCP 服务器

这是连接 Google Analytics 的关键步骤。

#### 1. 理解 ~/.gemini/settings.json 文件

`~/.gemini/settings.json` 是 Gemini CLI 的配置文件：
- **位置**：用户主目录下的 `.gemini` 文件夹
- **Windows**：`C:\Users\<USER>\.gemini\settings.json`
- **macOS/Linux**：`/Users/<USER>/.gemini/settings.json` 或 `/home/<USER>/.gemini/settings.json`

#### 2. 创建配置文件

如果文件不存在，需要手动创建：

```bash
# 创建 .gemini 目录（如果不存在）
mkdir -p ~/.gemini

# 创建 settings.json 文件
touch ~/.gemini/settings.json
```

#### 3. 基础配置

编辑 `~/.gemini/settings.json` 文件，添加以下内容：

```json
{
  "mcpServers": {
    "analytics-mcp": {
      "command": "pipx",
      "args": [
        "run",
        "--spec",
        "git+https://github.com/googleanalytics/google-analytics-mcp.git",
        "google-analytics-mcp"
      ]
    }
  }
}
```

#### 4. 高级配置（推荐）

如果你想指定特定的凭据和项目，使用以下配置：

```json
{
  "mcpServers": {
    "analytics-mcp": {
      "command": "pipx",
      "args": [
        "run",
        "--spec",
        "git+https://github.com/googleanalytics/google-analytics-mcp.git",
        "google-analytics-mcp"
      ],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "/path/to/your/credentials.json",
        "GOOGLE_CLOUD_PROJECT": "your-project-id"
      }
    }
  }
}
```

**重要说明**：
- 将 `/path/to/your/credentials.json` 替换为你的实际凭据文件路径
- 将 `your-project-id` 替换为你的 Google Cloud 项目 ID

#### 5. 验证配置文件

确保 JSON 格式正确：

```bash
# 使用 Python 验证 JSON 格式
python -m json.tool ~/.gemini/settings.json

# 或者使用 jq（如果已安装）
jq . ~/.gemini/settings.json
```

### 第六步：启动和测试

#### 1. 首次启动 Gemini CLI

```bash
# 启动 Gemini CLI
gemini
```

首次启动时，Gemini CLI 会要求你选择认证方式：
- 选择 "OAuth" 如果你使用个人 Google 账户
- 按照提示在浏览器中完成认证

#### 2. 验证 MCP 服务器

在 Gemini CLI 中输入：
```
/mcp
```

你应该看到 `analytics-mcp` 出现在可用服务器列表中。

#### 3. 测试连接

尝试以下测试命令：
```
what can the analytics-mcp server do?
```

如果配置正确，Gemini 会列出所有可用的 Google Analytics 功能。

## 实际使用示例

### 基础查询

```bash
# 启动 Gemini CLI
gemini

# 在 Gemini CLI 中输入以下查询：
```

**查询账户信息**：
```
Show me all my Google Analytics accounts and properties
```

**查询特定属性**：
```
Give me details about my Google Analytics property with 'website' in the name
```

**获取报告数据**：
```
What are the most popular pages on my website in the last 30 days?
```

**实时数据查询**：
```
Show me the current active users on my website right now
```

**自定义维度查询**：
```
What custom dimensions and metrics are configured in my GA4 property?
```

### 高级分析查询

```
# 用户行为分析
How many users converted from organic search in the last month?

# 流量来源分析
What are the top 5 traffic sources for my website this week?

# 事件分析
Which events are triggered most frequently on my site?

# 地理位置分析
Which countries generate the most traffic to my website?
```

## 可用工具

### 账户和属性信息
- `get_account_summaries`: 获取用户的 Google Analytics 账户和属性信息
- `get_property_details`: 返回属性详细信息
- `list_google_ads_links`: 返回属性的 Google Ads 账户链接列表

### 核心报告
- `run_report`: 使用 Data API 运行 Google Analytics 报告
- `get_custom_dimensions_and_metrics`: 获取特定属性的自定义维度和指标

### 实时报告
- `run_realtime_report`: 使用 Data API 运行 Google Analytics 实时报告

## 故障排除指南

### 常见问题及解决方案

#### 1. "analytics-mcp 服务器未找到"

**症状**：输入 `/mcp` 后看不到 analytics-mcp

**解决方案**：
```bash
# 检查 settings.json 文件是否存在
ls -la ~/.gemini/settings.json

# 检查 JSON 格式是否正确
python -m json.tool ~/.gemini/settings.json

# 检查 pipx 是否正确安装
pipx --version

# 重启 Gemini CLI
```

#### 2. "认证失败"错误

**症状**：MCP 服务器启动但无法访问 Google Analytics

**解决方案**：
```bash
# 检查当前认证状态
gcloud auth list

# 重新进行认证
gcloud auth application-default login \
  --scopes https://www.googleapis.com/auth/analytics.readonly,https://www.googleapis.com/auth/cloud-platform

# 检查项目设置
gcloud config list
```

#### 3. "API 未启用"错误

**症状**：提示 API 未启用或无权限

**解决方案**：
1. 访问 [Google Cloud Console](https://console.cloud.google.com/apis/library)
2. 确保选择了正确的项目
3. 搜索并启用：
   - Google Analytics Admin API
   - Google Analytics Data API

#### 4. "权限不足"错误

**症状**：能连接但无法查询数据

**解决方案**：
1. 确保你的 Google 账户有 Google Analytics 访问权限
2. 检查 OAuth 客户端的权限范围
3. 验证服务账户是否被添加到 Google Analytics 属性中

#### 5. pipx 相关问题

**症状**：pipx 命令不存在或无法运行

**解决方案**：
```bash
# 重新安装 pipx
python -m pip install --user pipx

# 添加 pipx 到 PATH（如果需要）
python -m pipx ensurepath

# 重新加载 shell 配置
source ~/.bashrc  # 或 ~/.zshrc
```

### 调试步骤

#### 逐步诊断

1. **检查基础环境**：
   ```bash
   # 检查 Python 版本
   python --version

   # 检查 Node.js 版本
   node --version

   # 检查 Gemini CLI 版本
   gemini --version
   ```

2. **检查认证状态**：
   ```bash
   # 查看当前认证的账户
   gcloud auth list

   # 查看当前项目
   gcloud config get-value project

   # 测试 API 访问
   gcloud services list --enabled
   ```

3. **检查配置文件**：
   ```bash
   # 查看配置文件内容
   cat ~/.gemini/settings.json

   # 验证 JSON 格式
   python -c "import json; print(json.load(open('~/.gemini/settings.json'.replace('~', '$HOME'))))"
   ```

4. **手动测试 MCP 服务器**：
   ```bash
   # 直接运行 MCP 服务器测试
   pipx run --spec git+https://github.com/googleanalytics/google-analytics-mcp.git google-analytics-mcp --help
   ```

### 日志和调试

#### 启用详细日志

```bash
# 设置环境变量启用调试模式
export DEBUG=1
export GOOGLE_CLOUD_LOGGING_ENABLED=true

# 启动 Gemini CLI
gemini
```

#### 查看日志文件

```bash
# Gemini CLI 日志位置（可能因系统而异）
# macOS/Linux
tail -f ~/.gemini/logs/gemini.log

# Windows
type %USERPROFILE%\.gemini\logs\gemini.log
```

## 完整配置示例

### Windows 系统完整示例

```powershell
# 1. 安装 Node.js（从 nodejs.org 下载）
# 2. 安装 Gemini CLI
npm install -g @google/gemini-cli

# 3. 安装 Python 和 pipx
python -m pip install --user pipx

# 4. 创建配置目录
mkdir $env:USERPROFILE\.gemini

# 5. 创建配置文件
@"
{
  "mcpServers": {
    "analytics-mcp": {
      "command": "pipx",
      "args": [
        "run",
        "--spec",
        "git+https://github.com/googleanalytics/google-analytics-mcp.git",
        "google-analytics-mcp"
      ],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "C:\\path\\to\\your\\credentials.json",
        "GOOGLE_CLOUD_PROJECT": "your-project-id"
      }
    }
  }
}
"@ | Out-File -FilePath "$env:USERPROFILE\.gemini\settings.json" -Encoding UTF8

# 6. 启动 Gemini CLI
gemini
```

### macOS/Linux 系统完整示例

```bash
# 1. 安装 Node.js
# macOS: brew install node
# Ubuntu: sudo apt install nodejs npm

# 2. 安装 Gemini CLI
npm install -g @google/gemini-cli

# 3. 安装 pipx
python3 -m pip install --user pipx

# 4. 创建配置目录和文件
mkdir -p ~/.gemini

# 5. 创建配置文件
cat > ~/.gemini/settings.json << 'EOF'
{
  "mcpServers": {
    "analytics-mcp": {
      "command": "pipx",
      "args": [
        "run",
        "--spec",
        "git+https://github.com/googleanalytics/google-analytics-mcp.git",
        "google-analytics-mcp"
      ],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "/path/to/your/credentials.json",
        "GOOGLE_CLOUD_PROJECT": "your-project-id"
      }
    }
  }
}
EOF

# 6. 启动 Gemini CLI
gemini
```

## 高级配置选项

### 多个 MCP 服务器配置

```json
{
  "mcpServers": {
    "analytics-mcp": {
      "command": "pipx",
      "args": [
        "run",
        "--spec",
        "git+https://github.com/googleanalytics/google-analytics-mcp.git",
        "google-analytics-mcp"
      ]
    },
    "github-mcp": {
      "command": "npx",
      "args": [
        "@modelcontextprotocol/server-github"
      ],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "your-github-token"
      }
    }
  }
}
```

### 项目级配置

除了全局配置，你还可以在项目根目录创建 `.gemini/settings.json` 文件，用于项目特定的配置。

## 参考资源

### 官方文档
- [Gemini CLI 官方仓库](https://github.com/google-gemini/gemini-cli)
- [Google Analytics MCP Server](https://github.com/googleanalytics/google-analytics-mcp)
- [Model Context Protocol 官方文档](https://modelcontextprotocol.io)

### API 文档
- [Google Analytics Admin API](https://developers.google.com/analytics/devguides/config/admin/v1)
- [Google Analytics Data API](https://developers.google.com/analytics/devguides/reporting/data/v1)
- [Google Cloud 认证文档](https://cloud.google.com/docs/authentication/provide-credentials-adc)

### 社区资源
- [Discord 讨论频道](https://discord.com/channels/971845904002871346/1398002598665257060)
- [GitHub Issues](https://github.com/googleanalytics/google-analytics-mcp/issues)

## 总结

通过本指南，你应该能够：
1. ✅ 安装并配置 Gemini CLI
2. ✅ 设置 Google Cloud 项目和 API
3. ✅ 配置身份验证
4. ✅ 创建和配置 `~/.gemini/settings.json` 文件
5. ✅ 成功连接 Google Analytics MCP 服务器
6. ✅ 开始查询你的 Google Analytics 数据

如果遇到问题，请参考故障排除部分或在 GitHub 上提交 issue。

## 许可证

本文档基于 Apache-2.0 许可证。Google Analytics MCP Server 项目同样使用 Apache-2.0 许可证。
