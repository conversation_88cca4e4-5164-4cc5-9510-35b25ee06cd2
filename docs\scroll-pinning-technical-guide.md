# GSAP ScrollTrigger 滚动锁定技术深度解析

## 📋 目录

1. [滚动锁定机制原理](#滚动锁定机制原理)
2. [组件边界控制策略](#组件边界控制策略)
3. [动画状态管理系统](#动画状态管理系统)
4. [技术实现细节](#技术实现细节)
5. [多层动画协同机制](#多层动画协同机制)
6. [实际应用案例](#实际应用案例)
7. [最佳实践指南](#最佳实践指南)

## 滚动锁定机制原理

### 核心实现代码

在 Capsules 项目中，滚动锁定通过以下代码实现：

```javascript
cards.forEach((card, index) => {
  const isLastCard = index === cards.length - 1
  ScrollTrigger.create({
    trigger: card,              // 触发元素
    start: "top top",           // 开始位置：卡片顶部到达视窗顶部
    end: isLastCard ? "+=100vh" : "top top",  // 结束条件
    endTrigger: isLastCard ? null : cards[cards.length - 1], // 结束触发器
    pin: true,                  // 启用滚动锁定
    pinSpacing: isLastCard,     // 是否保留滚动空间
  })
})
```

### 技术原理分析

#### 1. **pin: true 的工作机制**

```javascript
// pin: true 的内部工作原理（简化版）
function pinElement(element, trigger) {
  // 1. 计算元素的原始位置
  const originalPosition = element.getBoundingClientRect()
  
  // 2. 当触发条件满足时，将元素设置为 fixed 定位
  element.style.position = 'fixed'
  element.style.top = '0px'
  element.style.left = originalPosition.left + 'px'
  
  // 3. 创建占位元素保持文档流
  const placeholder = document.createElement('div')
  placeholder.style.height = element.offsetHeight + 'px'
  element.parentNode.insertBefore(placeholder, element)
}
```

#### 2. **pinSpacing 参数的作用**

```javascript
// pinSpacing 控制是否在文档中保留滚动空间
pinSpacing: true   // 保留空间，其他元素正常流动
pinSpacing: false  // 不保留空间，后续元素会上移填补
```

### 滚动锁定的视觉效果

```
初始状态：
┌─────────────────┐
│   视窗顶部      │
├─────────────────┤
│                 │
│   Card 1        │ ← 正常文档流中
│                 │
├─────────────────┤
│   Card 2        │
└─────────────────┘

锁定状态：
┌─────────────────┐
│   Card 1        │ ← 固定在视窗顶部 (position: fixed)
├─────────────────┤
│                 │
│   [占位空间]    │ ← pinSpacing 控制是否显示
│                 │
├─────────────────┤
│   Card 2        │ ← 继续向上滚动
└─────────────────┘
```

## 组件边界控制策略

### 精确的触发区域控制

项目中使用了5个不同的 ScrollTrigger 实例来控制不同的动画效果，每个都有精确的边界控制：

#### 1. **主卡片动画边界**

```javascript
// 第一张卡片的特殊处理 - 300vh 的动画区域
ScrollTrigger.create({
  trigger: introCard,
  start: "top top",      // 卡片顶部到达视窗顶部时开始
  end: "+=300vh",        // 持续 3 个视窗高度的滚动距离
  onUpdate: (self) => {
    const progress = self.progress  // 0 到 1 的进度值
    // 基于进度的动画逻辑
  }
})
```

#### 2. **卡片过渡边界控制**

```javascript
// 前一张卡片的淡出控制
ScrollTrigger.create({
  trigger: cards[index + 1],  // 以下一张卡片为触发器
  start: "top bottom",        // 下一张卡片顶部到达视窗底部时开始
  end: "top top",            // 下一张卡片顶部到达视窗顶部时结束
  onUpdate: (self) => {
    const progress = self.progress
    // 前一张卡片的淡出动画
    gsap.set(cardImgWrapper, {
      scale: 1 - progress * 0.25,
      opacity: 1 - progress
    })
  }
})
```

#### 3. **当前卡片变形边界**

```javascript
// 当前卡片的图像变形控制
ScrollTrigger.create({
  trigger: card,
  start: "top bottom",    // 卡片顶部到达视窗底部时开始
  end: "top top",        // 卡片顶部到达视窗顶部时结束
  onUpdate: (self) => {
    const progress = self.progress
    gsap.set(cardImg, { scale: 2 - progress })  // 2.0 → 1.0
    gsap.set(imgContainer, { 
      borderRadius: 150 - progress * 125 + "px"  // 150px → 25px
    })
  }
})
```

### 边界控制的关键技术

#### **start/end 参数详解**

```javascript
// 参数格式："{trigger位置} {viewport位置}"
start: "top top"      // 触发元素顶部 到达 视窗顶部
start: "top bottom"   // 触发元素顶部 到达 视窗底部
start: "center center" // 触发元素中心 到达 视窗中心

end: "bottom top"     // 触发元素底部 到达 视窗顶部
end: "+=300vh"        // 相对于 start 位置向下 300vh
```

#### **endTrigger 的高级用法**

```javascript
ScrollTrigger.create({
  trigger: card,
  start: "top top",
  end: isLastCard ? "+=100vh" : "top top",
  endTrigger: isLastCard ? null : cards[cards.length - 1],  // 以最后一张卡片为结束触发器
  pin: true
})
```

## 动画状态管理系统

### 状态切换的精确控制

#### 1. **基于进度的状态管理**

```javascript
ScrollTrigger.create({
  trigger: introCard,
  start: "top top",
  end: "+=300vh",
  onUpdate: (self) => {
    const progress = self.progress  // 核心进度值 0-1
    
    // 图像缩放状态：0.5 → 1.0
    const imgScale = 0.5 + progress * 0.5
    
    // 圆角变化状态：400px → 25px
    const borderRadius = 400 - progress * 375
    
    // 内部图像缩放：1.5 → 1.0
    const innerImgScale = 1.5 - progress * 0.5
    
    // 应用状态变化
    gsap.set(cardImgWrapper, {
      scale: imgScale,
      borderRadius: `${borderRadius}px`
    })
    gsap.set(cardImg, { scale: innerImgScale })
  }
})
```

#### 2. **条件状态管理**

```javascript
// 跑马灯的分段淡入淡出控制
if (imgScale >= 0.5 && imgScale <= 0.75) {
  // 在特定范围内进行淡出
  const fadeProgress = (imgScale - 0.5) / (0.75 - 0.5)
  gsap.set(marqee, { opacity: 1 - fadeProgress })
} else if (imgScale < 0.5) {
  gsap.set(marqee, { opacity: 1 })      // 完全显示
} else if (imgScale > 0.75) {
  gsap.set(marqee, { opacity: 0 })      // 完全隐藏
}
```

#### 3. **内容显示状态管理**

```javascript
// 使用标志位防止重复触发
if (progress >= 1 && !introCard.contentRevealed) {
  introCard.contentRevealed = true
  animateContentIn(titleChars, description)
}
if (progress < 1 && introCard.contentRevealed) {
  introCard.contentRevealed = false
  animateContentOut(titleChars, description)
}
```

### 回调函数的状态管理

```javascript
ScrollTrigger.create({
  trigger: card,
  start: "top top",
  onEnter: () => {
    // 进入状态：显示内容
    animateContentIn(cardTitleChars, cardDescription)
  },
  onLeaveBack: () => {
    // 返回状态：隐藏内容
    animateContentOut(cardTitleChars, cardDescription)
  }
})
```

## 技术实现细节

### ScrollTrigger.create() 完整配置解析

```javascript
ScrollTrigger.create({
  // === 基础配置 ===
  trigger: element,           // 触发元素
  start: "top top",          // 开始位置
  end: "bottom top",         // 结束位置
  endTrigger: null,          // 结束触发器（可选）
  
  // === 滚动锁定配置 ===
  pin: true,                 // 启用锁定
  pinSpacing: true,          // 保留滚动空间
  pinType: "fixed",          // 锁定类型：fixed | transform
  
  // === 动画配置 ===
  scrub: false,              // 是否与滚动同步
  snap: false,               // 是否启用吸附
  
  // === 回调函数 ===
  onUpdate: (self) => {      // 滚动更新时
    const progress = self.progress
    // 动画逻辑
  },
  onEnter: () => {},         // 进入触发区域
  onLeave: () => {},         // 离开触发区域
  onEnterBack: () => {},     // 反向进入
  onLeaveBack: () => {},     // 反向离开
  
  // === 调试配置 ===
  markers: false,            // 显示调试标记
  id: "unique-id"           // 唯一标识符
})
```

### pin 和 pinSpacing 的深度解析

#### **pin: true 的内部机制**

```javascript
// GSAP 内部实现的简化版本
class ScrollTriggerPin {
  constructor(element, config) {
    this.element = element
    this.originalStyles = this.saveOriginalStyles()
    this.placeholder = null
  }
  
  pin() {
    // 1. 创建占位元素
    this.placeholder = document.createElement('div')
    this.placeholder.style.height = this.element.offsetHeight + 'px'
    
    // 2. 设置固定定位
    this.element.style.position = 'fixed'
    this.element.style.top = '0px'
    this.element.style.zIndex = '1000'
    
    // 3. 插入占位元素
    if (this.config.pinSpacing) {
      this.element.parentNode.insertBefore(this.placeholder, this.element)
    }
  }
  
  unpin() {
    // 恢复原始状态
    this.restoreOriginalStyles()
    if (this.placeholder) {
      this.placeholder.remove()
    }
  }
}
```

#### **pinSpacing 的视觉对比**

```css
/* pinSpacing: true - 保留空间 */
.pinned-element {
  position: fixed;
  top: 0;
}
.placeholder {
  height: 100vh; /* 保持原有高度 */
  visibility: hidden;
}

/* pinSpacing: false - 不保留空间 */
.pinned-element {
  position: fixed;
  top: 0;
}
/* 无占位元素，后续内容直接上移 */
```

### 防止动画干扰的技术策略

#### 1. **独立的触发器设计**

```javascript
// 每个动画效果使用独立的 ScrollTrigger 实例
const triggers = [
  // 主动画触发器
  {
    trigger: introCard,
    start: "top top",
    end: "+=300vh",
    purpose: "主图像变形和跑马灯控制"
  },
  
  // 锁定触发器
  {
    trigger: card,
    start: "top top", 
    end: "top top",
    pin: true,
    purpose: "滚动锁定效果"
  },
  
  // 过渡触发器
  {
    trigger: cards[index + 1],
    start: "top bottom",
    end: "top top", 
    purpose: "卡片过渡动画"
  }
]
```

#### 2. **精确的时间线控制**

```javascript
// 通过不同的 start/end 参数避免重叠
const animationTimeline = {
  "卡片进入": {
    start: "top bottom",    // 卡片底部进入视窗
    end: "top top",        // 卡片顶部到达视窗顶部
    duration: "1个视窗高度"
  },
  
  "卡片锁定": {
    start: "top top",      // 卡片顶部到达视窗顶部
    end: "bottom top",     // 卡片底部离开视窗顶部
    duration: "卡片高度"
  },
  
  "卡片退出": {
    start: "bottom top",   // 卡片底部到达视窗顶部
    end: "bottom bottom",  // 卡片底部离开视窗底部
    duration: "1个视窗高度"
  }
}
```

### 滚动进度计算和同步

#### **progress 值的计算原理**

```javascript
// ScrollTrigger 内部的进度计算
function calculateProgress(scrollY, startY, endY) {
  if (scrollY <= startY) return 0
  if (scrollY >= endY) return 1
  return (scrollY - startY) / (endY - startY)
}

// 实际应用中的进度映射
ScrollTrigger.create({
  start: "top top",      // startY = 卡片顶部位置
  end: "+=300vh",        // endY = startY + 300vh
  onUpdate: (self) => {
    // self.progress 是 0-1 的标准化值
    const progress = self.progress
    
    // 映射到具体的动画值
    const scale = 0.5 + progress * 0.5        // 0.5 → 1.0
    const radius = 400 - progress * 375       // 400 → 25
    const opacity = 1 - progress             // 1.0 → 0.0
  }
})
```

## 多层动画协同机制

### 五层动画系统架构

```javascript
// 动画层级结构
const animationLayers = {
  // Layer 1: 主动画控制层
  mainAnimation: {
    trigger: introCard,
    range: "300vh",
    controls: ["图像变形", "跑马灯", "内容显示"]
  },
  
  // Layer 2: 滚动锁定层
  pinningLayer: {
    trigger: "each card",
    range: "card height",
    controls: ["位置固定", "空间管理"]
  },
  
  // Layer 3: 过渡动画层
  transitionLayer: {
    trigger: "next card",
    range: "1vh",
    controls: ["前卡片淡出", "当前卡片变形"]
  },
  
  // Layer 4: 内容动画层
  contentLayer: {
    trigger: "card enter/leave",
    range: "instant",
    controls: ["文字动画", "描述显示"]
  },
  
  // Layer 5: 细节动画层
  detailLayer: {
    trigger: "specific progress",
    range: "conditional",
    controls: ["跑马灯淡入淡出", "状态标志"]
  }
}
```

### 协同工作的时序图

```
滚动进度:  0%    25%    50%    75%    100%
         │      │      │      │      │
Layer 1: ├──────┼──────┼──────┼──────┤  主动画 (300vh)
Layer 2: ├──────────────────────────┤  锁定效果
Layer 3:        ├──────┤              过渡动画
Layer 4:               ├──┤           内容动画
Layer 5:        ├──┤                  跑马灯控制
```

## 实际应用案例

### 案例1：产品展示页面

```javascript
// 产品卡片滚动锁定实现
function createProductShowcase() {
  const products = gsap.utils.toArray('.product-card')
  
  products.forEach((product, index) => {
    // 滚动锁定
    ScrollTrigger.create({
      trigger: product,
      start: "top top",
      end: () => index === products.length - 1 ? "+=50vh" : "top top",
      endTrigger: index === products.length - 1 ? null : products[products.length - 1],
      pin: true,
      pinSpacing: index === products.length - 1
    })
    
    // 产品图像动画
    const productImage = product.querySelector('.product-image')
    ScrollTrigger.create({
      trigger: product,
      start: "top bottom",
      end: "top top",
      onUpdate: (self) => {
        const progress = self.progress
        gsap.set(productImage, {
          scale: 1.5 - progress * 0.3,
          rotationY: progress * 15
        })
      }
    })
    
    // 产品信息动画
    const productInfo = product.querySelector('.product-info')
    ScrollTrigger.create({
      trigger: product,
      start: "top center",
      onEnter: () => {
        gsap.to(productInfo, {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power3.out"
        })
      },
      onLeaveBack: () => {
        gsap.to(productInfo, {
          opacity: 0,
          y: 50,
          duration: 0.5
        })
      }
    })
  })
}
```

### 案例2：时间线展示

```javascript
// 时间线事件滚动锁定
function createTimelineShowcase() {
  const timelineItems = gsap.utils.toArray('.timeline-item')
  
  timelineItems.forEach((item, index) => {
    // 时间线项目锁定
    ScrollTrigger.create({
      trigger: item,
      start: "center center",
      end: "bottom center",
      pin: true,
      pinSpacing: false
    })
    
    // 时间线连接线动画
    const connector = item.querySelector('.timeline-connector')
    ScrollTrigger.create({
      trigger: item,
      start: "top bottom",
      end: "center center",
      onUpdate: (self) => {
        const progress = self.progress
        gsap.set(connector, {
          scaleY: progress,
          transformOrigin: "top center"
        })
      }
    })
    
    // 内容渐现动画
    const content = item.querySelector('.timeline-content')
    ScrollTrigger.create({
      trigger: item,
      start: "center center",
      onEnter: () => {
        gsap.fromTo(content, 
          { opacity: 0, x: -50 },
          { opacity: 1, x: 0, duration: 1, ease: "power2.out" }
        )
      }
    })
  })
}
```

### 案例3：作品集展示

```javascript
// 作品集滚动锁定展示
function createPortfolioShowcase() {
  const portfolioItems = gsap.utils.toArray('.portfolio-item')
  
  portfolioItems.forEach((item, index) => {
    const isLast = index === portfolioItems.length - 1
    
    // 作品锁定效果
    ScrollTrigger.create({
      trigger: item,
      start: "top top",
      end: isLast ? "+=100vh" : "top top",
      endTrigger: isLast ? null : portfolioItems[portfolioItems.length - 1],
      pin: true,
      pinSpacing: isLast
    })
    
    // 作品图像视差效果
    const portfolioImage = item.querySelector('.portfolio-image')
    ScrollTrigger.create({
      trigger: item,
      start: "top bottom",
      end: "bottom top",
      onUpdate: (self) => {
        const progress = self.progress
        gsap.set(portfolioImage, {
          y: progress * -100,
          scale: 1 + progress * 0.1
        })
      }
    })
    
    // 作品信息分层显示
    const portfolioTitle = item.querySelector('.portfolio-title')
    const portfolioDesc = item.querySelector('.portfolio-description')
    const portfolioTags = item.querySelector('.portfolio-tags')
    
    ScrollTrigger.create({
      trigger: item,
      start: "top center",
      onEnter: () => {
        const tl = gsap.timeline()
        tl.to(portfolioTitle, { opacity: 1, y: 0, duration: 0.6 })
          .to(portfolioDesc, { opacity: 1, y: 0, duration: 0.6 }, "-=0.3")
          .to(portfolioTags, { opacity: 1, y: 0, duration: 0.6 }, "-=0.3")
      },
      onLeaveBack: () => {
        gsap.to([portfolioTitle, portfolioDesc, portfolioTags], {
          opacity: 0,
          y: 30,
          duration: 0.4,
          stagger: 0.1
        })
      }
    })
  })
}
```

## 最佳实践指南

### 1. **性能优化策略**

```javascript
// 使用 will-change 提示浏览器优化
.pinned-element {
  will-change: transform;
}

// 避免在 onUpdate 中进行复杂计算
ScrollTrigger.create({
  onUpdate: (self) => {
    // ❌ 避免：复杂计算
    const complexValue = Math.sin(self.progress * Math.PI) * 100
    
    // ✅ 推荐：简单映射
    const simpleValue = self.progress * 100
  }
})

// 使用 gsap.set 而非 gsap.to 进行即时更新
ScrollTrigger.create({
  onUpdate: (self) => {
    // ✅ 即时更新，无动画开销
    gsap.set(element, { x: self.progress * 100 })
    
    // ❌ 避免：会创建新的动画实例
    gsap.to(element, { x: self.progress * 100, duration: 0 })
  }
})
```

### 2. **调试和开发技巧**

```javascript
// 启用调试标记
ScrollTrigger.config({
  markers: true
})

// 为每个触发器添加唯一ID
ScrollTrigger.create({
  trigger: element,
  start: "top top",
  end: "bottom top",
  id: "card-pin-" + index,  // 便于调试识别
  markers: {
    startColor: "green",
    endColor: "red",
    fontSize: "12px"
  }
})

// 性能监控
let frameCount = 0
ScrollTrigger.create({
  onUpdate: () => {
    frameCount++
    if (frameCount % 60 === 0) {
      console.log('ScrollTrigger FPS check:', frameCount)
    }
  }
})
```

### 3. **响应式设计适配**

```javascript
// 使用 matchMedia 进行响应式适配
let mm = gsap.matchMedia()

mm.add("(min-width: 1024px)", () => {
  // 桌面端：完整的滚动锁定效果
  ScrollTrigger.create({
    trigger: card,
    start: "top top",
    end: "bottom top",
    pin: true,
    pinSpacing: true
  })
})

mm.add("(max-width: 1023px)", () => {
  // 移动端：简化的滚动效果
  ScrollTrigger.create({
    trigger: card,
    start: "top center",
    end: "bottom center",
    // 不使用 pin，改用简单的动画
    onUpdate: (self) => {
      gsap.set(card, { 
        scale: 0.9 + self.progress * 0.1 
      })
    }
  })
})
```

### 4. **内存管理和清理**

```javascript
// 存储 ScrollTrigger 实例以便清理
const scrollTriggers = []

function createScrollAnimations() {
  const trigger = ScrollTrigger.create({
    trigger: element,
    start: "top top",
    end: "bottom top",
    pin: true
  })
  
  scrollTriggers.push(trigger)
}

// 页面卸载时清理
function cleanup() {
  scrollTriggers.forEach(trigger => trigger.kill())
  scrollTriggers.length = 0
  ScrollTrigger.refresh()
}

window.addEventListener('beforeunload', cleanup)
```

### 5. **错误处理和容错机制**

```javascript
function createSafeScrollTrigger(config) {
  try {
    // 验证必要的元素存在
    if (!config.trigger || !document.contains(config.trigger)) {
      console.warn('ScrollTrigger: trigger element not found')
      return null
    }
    
    return ScrollTrigger.create({
      ...config,
      onUpdate: (self) => {
        try {
          config.onUpdate?.(self)
        } catch (error) {
          console.error('ScrollTrigger onUpdate error:', error)
        }
      }
    })
  } catch (error) {
    console.error('ScrollTrigger creation failed:', error)
    return null
  }
}
```

### 6. **高级动画技巧**

```javascript
// 动画缓存优化
const animationCache = new Map()

function getCachedAnimation(key, createFn) {
  if (!animationCache.has(key)) {
    animationCache.set(key, createFn())
  }
  return animationCache.get(key)
}

// 使用示例
ScrollTrigger.create({
  onUpdate: (self) => {
    const animation = getCachedAnimation('scale-animation', () => {
      return gsap.to(element, {
        scale: 1.2,
        duration: 0.3,
        paused: true
      })
    })

    animation.progress(self.progress)
  }
})
```

### 7. **复杂场景处理**

```javascript
// 处理动态内容和异步加载
function createDynamicScrollTrigger(container) {
  let currentTriggers = []

  function refreshTriggers() {
    // 清理现有触发器
    currentTriggers.forEach(trigger => trigger.kill())
    currentTriggers = []

    // 重新创建触发器
    const items = container.querySelectorAll('.dynamic-item')
    items.forEach((item, index) => {
      const trigger = ScrollTrigger.create({
        trigger: item,
        start: "top center",
        end: "bottom center",
        pin: true,
        onUpdate: (self) => {
          // 动画逻辑
        }
      })
      currentTriggers.push(trigger)
    })
  }

  // 监听内容变化
  const observer = new MutationObserver(refreshTriggers)
  observer.observe(container, {
    childList: true,
    subtree: true
  })

  // 初始创建
  refreshTriggers()

  return {
    refresh: refreshTriggers,
    destroy: () => {
      observer.disconnect()
      currentTriggers.forEach(trigger => trigger.kill())
    }
  }
}
```

## 高级应用模式

### 1. **嵌套滚动锁定**

```javascript
// 主容器锁定
ScrollTrigger.create({
  trigger: ".main-section",
  start: "top top",
  end: "bottom bottom",
  pin: true,
  pinSpacing: false
})

// 内部元素独立动画
ScrollTrigger.create({
  trigger: ".inner-element",
  start: "top center",
  end: "bottom center",
  onUpdate: (self) => {
    // 内部动画不受主容器锁定影响
    gsap.set(".inner-element", {
      rotation: self.progress * 360
    })
  }
})
```

### 2. **条件性滚动锁定**

```javascript
function createConditionalPin(element, condition) {
  let currentTrigger = null

  function updatePin() {
    // 清理现有触发器
    if (currentTrigger) {
      currentTrigger.kill()
      currentTrigger = null
    }

    // 根据条件创建触发器
    if (condition()) {
      currentTrigger = ScrollTrigger.create({
        trigger: element,
        start: "top top",
        end: "bottom top",
        pin: true
      })
    }
  }

  // 监听条件变化
  window.addEventListener('resize', updatePin)
  updatePin()

  return {
    update: updatePin,
    destroy: () => {
      if (currentTrigger) currentTrigger.kill()
      window.removeEventListener('resize', updatePin)
    }
  }
}

// 使用示例：只在桌面端启用锁定
const conditionalPin = createConditionalPin(
  document.querySelector('.card'),
  () => window.innerWidth > 1024
)
```

### 3. **滚动锁定与路由集成**

```javascript
// 与 SPA 路由系统集成
class ScrollTriggerRouter {
  constructor() {
    this.triggers = new Map()
    this.currentRoute = null
  }

  registerRoute(routeName, triggerConfigs) {
    this.triggers.set(routeName, triggerConfigs)
  }

  activateRoute(routeName) {
    // 清理当前路由的触发器
    if (this.currentRoute) {
      this.deactivateRoute(this.currentRoute)
    }

    // 激活新路由的触发器
    const configs = this.triggers.get(routeName)
    if (configs) {
      configs.forEach(config => {
        ScrollTrigger.create(config)
      })
    }

    this.currentRoute = routeName
    ScrollTrigger.refresh()
  }

  deactivateRoute(routeName) {
    ScrollTrigger.getAll().forEach(trigger => {
      if (trigger.vars.routeName === routeName) {
        trigger.kill()
      }
    })
  }
}

// 使用示例
const router = new ScrollTriggerRouter()

router.registerRoute('home', [
  {
    trigger: '.hero-section',
    start: 'top top',
    end: 'bottom top',
    pin: true,
    routeName: 'home'
  }
])

router.activateRoute('home')
```

## 故障排除指南

### 常见问题及解决方案

#### 1. **滚动锁定不生效**

```javascript
// 问题诊断
function diagnosePinIssues(element) {
  console.log('Element exists:', !!element)
  console.log('Element in DOM:', document.contains(element))
  console.log('Element dimensions:', element.getBoundingClientRect())
  console.log('ScrollTrigger instances:', ScrollTrigger.getAll().length)

  // 检查 CSS 冲突
  const computedStyle = getComputedStyle(element)
  console.log('Position:', computedStyle.position)
  console.log('Transform:', computedStyle.transform)
  console.log('Z-index:', computedStyle.zIndex)
}

// 解决方案
ScrollTrigger.create({
  trigger: element,
  start: "top top",
  end: "bottom top",
  pin: true,
  pinType: "fixed", // 强制使用 fixed 定位
  anticipatePin: 1,  // 提前准备锁定
  onRefresh: () => {
    console.log('ScrollTrigger refreshed')
  }
})
```

#### 2. **性能问题**

```javascript
// 性能监控和优化
const performanceMonitor = {
  frameCount: 0,
  lastTime: performance.now(),

  monitor() {
    this.frameCount++
    const currentTime = performance.now()

    if (currentTime - this.lastTime >= 1000) {
      const fps = this.frameCount
      console.log(`ScrollTrigger FPS: ${fps}`)

      if (fps < 30) {
        console.warn('Performance issue detected')
        this.optimizePerformance()
      }

      this.frameCount = 0
      this.lastTime = currentTime
    }
  },

  optimizePerformance() {
    // 减少动画复杂度
    ScrollTrigger.config({
      autoRefreshEvents: "visibilitychange,DOMContentLoaded,load"
    })

    // 启用批量更新
    ScrollTrigger.batch(".animated-element", {
      onEnter: (elements) => {
        gsap.from(elements, {
          opacity: 0,
          y: 50,
          stagger: 0.1
        })
      }
    })
  }
}

// 在 ScrollTrigger 中使用监控
ScrollTrigger.create({
  onUpdate: () => {
    performanceMonitor.monitor()
  }
})
```

#### 3. **移动端兼容性**

```javascript
// 移动端优化策略
function createMobileOptimizedPin(element) {
  const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)

  if (isMobile) {
    // 移动端使用简化版本
    ScrollTrigger.create({
      trigger: element,
      start: "top center",
      end: "bottom center",
      onUpdate: (self) => {
        // 使用 transform 而非 position: fixed
        gsap.set(element, {
          y: -self.progress * 50
        })
      }
    })
  } else {
    // 桌面端使用完整锁定
    ScrollTrigger.create({
      trigger: element,
      start: "top top",
      end: "bottom top",
      pin: true
    })
  }
}
```

## 总结

GSAP ScrollTrigger 的滚动锁定技术通过以下核心机制实现：

1. **pin: true** 将元素固定在视窗中的特定位置
2. **精确的边界控制** 通过 start/end 参数定义动画范围
3. **多层动画协同** 使用多个 ScrollTrigger 实例处理不同效果
4. **状态管理系统** 基于滚动进度进行精确的动画控制
5. **性能优化策略** 使用硬件加速和高效的动画属性

### 技术优势对比

| 特性 | 传统滚动动画 | ScrollTrigger 滚动锁定 |
|------|-------------|----------------------|
| 精确控制 | ❌ 基于事件监听 | ✅ 基于滚动进度 |
| 性能表现 | ❌ 频繁重排重绘 | ✅ 硬件加速优化 |
| 开发效率 | ❌ 复杂的计算逻辑 | ✅ 声明式配置 |
| 浏览器兼容 | ❌ 需要大量兼容代码 | ✅ 内置兼容处理 |
| 调试便利性 | ❌ 难以调试 | ✅ 可视化调试工具 |

### 最佳实践总结

1. **合理使用 pinSpacing** - 根据设计需求选择是否保留滚动空间
2. **精确控制动画边界** - 使用准确的 start/end 参数避免动画冲突
3. **优化性能表现** - 使用 will-change 和高效的动画属性
4. **做好错误处理** - 添加容错机制和调试信息
5. **响应式适配** - 针对不同设备提供合适的动画效果

通过掌握这些技术，你可以创建出专业级的滚动动画效果，为用户提供沉浸式的交互体验。滚动锁定技术特别适用于产品展示、作品集、时间线等需要突出内容展示的场景。
